# API Endpoint Fix Plan

## Problem Statement
1. **Incorrect Endpoints**: Many API endpoints are missing the `/orchestrator` prefix
2. **Duplicate Endpoints**: Same endpoints are repeated across multiple spec files
3. **404 Errors**: Endpoints returning 404 due to incorrect paths

## Root Cause Analysis
Based on the router configuration in `api/router/router.go`, all API endpoints should be prefixed with `/orchestrator`. The router sets up subrouters like:
- `/orchestrator/user` for user management
- `/orchestrator/cluster` for cluster management
- `/orchestrator/app` for application management
- etc.

## Current Status

### ✅ Fixed Endpoints (Step 1 Complete)
- `/user/role/group/v2` → `/orchestrator/user/role/group/v2`
- `/user/role/group/v2/{id}` → `/orchestrator/user/role/group/v2/{id}`
- `/user/{id}` → `/orchestrator/user/{id}` ✅ **VERIFIED**
- `/user/v2` → `/orchestrator/user/v2` ✅ **VERIFIED**
- `/user` → `/orchestrator/user` ✅ **VERIFIED**
- `/user/v2/{id}` → `/orchestrator/user/v2/{id}` ✅ **VERIFIED**
- `/user/bulk` → `/orchestrator/user/bulk` ✅ **VERIFIED**
- `/user/detail/get` → `/orchestrator/user/detail/get` ✅ **VERIFIED**

### ✅ Fixed Endpoints (Step 2 Complete)
- `/user/role/group` → `/orchestrator/user/role/group` ✅ **VERIFIED**
- `/user/role/group/{id}` → `/orchestrator/user/role/group/{id}` ✅ **FIXED**
- `/user/role/group/detailed/get` → `/orchestrator/user/role/group/detailed/get` ✅ **FIXED**
- `/user/role/group/search` → `/orchestrator/user/role/group/search` ✅ **FIXED**
- `/user/role/group/bulk` → `/orchestrator/user/role/group/bulk` ✅ **FIXED**

### 🔄 Remaining Endpoints to Fix
Found **62 endpoints** that need the `/orchestrator` prefix added:

#### User Management (10 endpoints)
- `/user/check/roles`
- `/user/sync/orchestratortocasbin`
- `/user/update/trigger/terminal`
- `/user/role/cache`
- `/user/role/cache/invalidate`

#### Authentication (6 endpoints)
- `/login`
- `/auth/login`
- `/auth/callback`
- `/devtron/auth/verify`
- `/devtron/auth/verify/v2`
- `/api/v1/session`

#### SSO (5 endpoints)
- `/sso/create`
- `/sso/update`
- `/sso/list`
- `/sso/{id}`
- `/sso`

#### Cluster Management (8 endpoints)
- `/cluster`
- `/cluster/delete`
- `/cluster/auth-list`
- `/cluster/validate`
- `/cluster/saveClusters`
- `/cluster/{cluster_id}/env`

#### Environment Management (3 endpoints)
- `/env`
- `/env/delete`
- `/env/clusters`

#### Application Management (6 endpoints)
- `/app/labels/list`
- `/app/env/patch`
- `/app/workflow/clone`
- `/app/workflow`
- `/app/workflow/{app-wf-id}/app/{app-id}`

#### Kubernetes (10 endpoints)
- `/k8s/resource`
- `/k8s/resource/create`
- `/k8s/resource/delete`
- `/k8s/events`
- `/k8s/pods/logs/{podName}`
- `/k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}`
- `/k8s/api-resources/{clusterId}`
- `/k8s/resource/list`
- `/k8s/resources/rotate`
- `/k8s/resources/apply`

#### Batch Operations (7 endpoints)
- `/batch/{apiVersion}/{kind}/readme`
- `/batch/v1beta1/application/dryrun`
- `/batch/v1beta1/hibernate`
- `/batch/v1beta1/unhibernate`
- `/batch/v1beta1/deploy`
- `/batch/v1beta1/build`
- `/batch/v1beta1/application`

#### Other (7 endpoints)
- `/resource/history/deployment/cd-pipeline/v1`
- `/rbac/roles/default`
- `/refresh`
- `/admin/policy/default`
- `/api/dex/{path}`
- `/version`
- `/validate`
- `/config`
- `/notification`
- `/notification/recipient`
- `/notification/channel`

## Implementation Plan

### Step-by-Step Process
1. **Pick one API spec** - Start with the main `specs/swagger/openapi.yaml`
2. **Check for 404** - Test the endpoint (when server is available)
3. **Fix the path** - Add `/orchestrator` prefix
4. **Test again** - Verify the fix works
5. **Log results** - Document success/failure
6. **Stop** - Wait for user confirmation before proceeding

### Tools Created
1. **`fix_api_endpoints.sh`** - Interactive script to fix endpoints one by one
2. **`API_ENDPOINT_FIX_PLAN.md`** - This comprehensive plan document

### Testing Strategy
When the server is available:
```bash
# Test before fix
curl --location 'https://devtron-ent-2.devtron.info/user/role/group/v2' \
--header 'Accept: application/json' \
--header 'Cookie: argocd.token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQzOTM3MzQsImp0aSI6ImRiODUxYzg5LTg0YjUtNDhiOS1hYTcyLWQ0ZTA0MjRjN2U4MSIsImlhdCI6MTc1NDMwNzMzNCwiaXNzIjoiYXJnb2NkIiwibmJmIjoxNzU0MzA3MzM0LCJzdWIiOiJhZG1pbiJ9.wYGmONdpNUEjtAxXz_mViW44Rxh0YU3dax_SEuoAH5c'

# Test after fix  
curl --location 'https://devtron-ent-2.devtron.info/orchestrator/user/role/group/v2' \
--header 'Accept: application/json' \
--header 'Cookie: argocd.token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQzOTM3MzQsImp0aSI6ImRiODUxYzg5LTg0YjUtNDhiOS1hYTcyLWQ0ZTA0MjRjN2U4MSIsImlhdCI6MTc1NDMwNzMzNCwiaXNzIjoiYXJnb2NkIiwibmJmIjoxNzU0MzA3MzM0LCJzdWIiOiJhZG1pbiJ9.wYGmONdpNUEjtAxXz_mViW44Rxh0YU3dax_SEuoAH5c'
```

### Duplicate Endpoint Analysis
Need to identify and consolidate duplicate endpoints across:
- `specs/swagger/openapi.yaml` (main file)
- `specs/security/*.yaml` files
- Other spec files in subdirectories

## Next Steps

### Immediate Actions
1. ✅ **Completed**: Fixed first endpoint `/user/role/group/v2`
2. 🔄 **Next**: Run the interactive script to fix remaining endpoints
3. 📋 **Plan**: Identify and resolve duplicate endpoints

### Long-term Actions
1. **Automated Testing**: Create tests to verify all endpoints work
2. **Documentation**: Update API documentation with correct endpoints
3. **Validation**: Add CI/CD checks to prevent future endpoint issues

## Usage Instructions

### To Fix Endpoints One by One:
```bash
./fix_api_endpoints.sh
```

### To Test a Specific Endpoint:
```bash
# Test endpoint
curl --location 'https://devtron-ent-2.devtron.info/orchestrator/user/role/group/v2' \
--header 'Accept: application/json' \
--header 'Cookie: argocd.token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQzOTM3MzQsImp0aSI6ImRiODUxYzg5LTg0YjUtNDhiOS1hYTcyLWQ0ZTA0MjRjN2U4MSIsImlhdCI6MTc1NDMwNzMzNCwiaXNzIjoiYXJnb2NkIiwibmJmIjoxNzU0MzA3MzM0LCJzdWIiOiJhZG1pbiJ9.wYGmONdpNUEjtAxXz_mViW44Rxh0YU3dax_SEuoAH5c'
```

## Notes
- All endpoints should be prefixed with `/orchestrator` based on router configuration
- The script processes one endpoint at a time and waits for user confirmation
- A log file `api_fix_log.txt` tracks all changes
- Backup files are created and cleaned up automatically 