API Endpoint Fix Log - Started: $(date)
===============================================

2024-12-18: Fixed /user/role/group/v2 -> /orchestrator/user/role/group/v2 ✅
2024-12-18: Fixed /user/role/group/v2/{id} -> /orchestrator/user/role/group/v2/{id} ✅
2024-12-18: Fixed /user/v2 -> /orchestrator/user/v2 ✅
2024-12-18: Fixed /user -> /orchestrator/user ✅

TESTING RESULTS:
- /user/role/group/v2: ❌ 404 Not Found (incorrect path)
- /orchestrator/user/role/group/v2: ✅ 200 OK (correct path)

NEXT ENDPOINTS TO FIX:
- /user/{id}
- /user/v2/{id}
- /user/bulk
- /user/detail/get
- /user/role/group
- /user/role/group/{id}
- /user/role/group/detailed/get
- /user/role/group/search
- /user/role/group/bulk
- /user/check/roles
- /user/sync/orchestratortocasbin
- /user/update/trigger/terminal
- /user/role/cache
- /user/role/cache/invalidate
- /login
- /auth/login
- /auth/callback
- /devtron/auth/verify
- /devtron/auth/verify/v2
- /api/v1/session
- /sso/create
- /sso/update
- /sso/list
- /sso/{id}
- /sso
- /rbac/roles/default
- /refresh
- /admin/policy/default
- /cluster
- /cluster/delete
- /cluster/auth-list
- /cluster/validate
- /cluster/saveClusters
- /cluster/{cluster_id}/env
- /env
- /env/delete
- /env/clusters
- /app/labels/list
- /app/env/patch
- /app/workflow/clone
- /app/workflow
- /app/workflow/{app-wf-id}/app/{app-id}
- /k8s/resource
- /k8s/resource/create
- /k8s/resource/delete
- /k8s/events
- /k8s/pods/logs/{podName}
- /k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}
- /k8s/api-resources/{clusterId}
- /k8s/resource/list
- /k8s/resources/rotate
- /k8s/resources/apply
- /batch/{apiVersion}/{kind}/readme
- /batch/v1beta1/application/dryrun
- /batch/v1beta1/hibernate
- /batch/v1beta1/unhibernate
- /batch/v1beta1/deploy
- /batch/v1beta1/build
- /batch/v1beta1/application
- /resource/history/deployment/cd-pipeline/v1
- /version
- /validate
- /config
- /notification
- /notification/recipient
- /notification/channel

TOTAL ENDPOINTS FIXED: 4
TOTAL ENDPOINTS REMAINING: 63 