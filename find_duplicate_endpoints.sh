#!/bin/bash

# Script to find duplicate endpoints across different spec files
# This helps identify which endpoints are repeated and need consolidation

echo "Finding duplicate endpoints across spec files..."
echo "================================================"

# Create a temporary file to store all endpoints
TEMP_FILE=$(mktemp)

# Function to extract endpoints from a YAML file
extract_endpoints() {
    local file="$1"
    echo "Processing: $file"
    
    # Extract endpoints (lines starting with /)
    grep "^  /" "$file" 2>/dev/null | while read -r line; do
        # Clean up the endpoint path
        endpoint=$(echo "$line" | sed 's/^  //' | sed 's/:$//')
        echo "$endpoint|$file" >> "$TEMP_FILE"
    done
}

# Process all YAML files in specs directory
find specs -name "*.yaml" -o -name "*.yml" | while read -r file; do
    extract_endpoints "$file"
done

# Find duplicates
echo ""
echo "Duplicate endpoints found:"
echo "========================="

# Sort and find duplicates
sort "$TEMP_FILE" | awk -F'|' '{
    endpoint=$1
    file=$2
    count[endpoint]++
    files[endpoint] = files[endpoint] file ", "
}
END {
    for (endpoint in count) {
        if (count[endpoint] > 1) {
            print "Endpoint: " endpoint
            print "Count: " count[endpoint]
            print "Files: " substr(files[endpoint], 1, length(files[endpoint])-2)
            print "---"
        }
    }
}'

# Clean up
rm "$TEMP_FILE"

echo ""
echo "Analysis complete!" 