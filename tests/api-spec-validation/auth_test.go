package api_spec_validation

import (
	"net/http"
	"strings"
	"testing"

	"go.uber.org/zap"
)

func TestAuthenticationTokenInCookies(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	validator := NewAPISpecValidator("http://localhost:8080", logger.Sugar())

	// Set auth token
	authToken := "test-auth-token"
	validator.SetToken(authToken)

	// Create a test request
	req, err := http.NewRequest("GET", "http://localhost:8080/test", nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	// Apply authentication
	validator.setAuthentication(req)

	// Check that no Authorization header is set (cookie-only authentication)
	authHeader := req.Header.Get("Authorization")
	if authHeader != "" {
		t.Errorf("Expected no Authorization header in cookie-only mode, got '%s'", authHeader)
	}

	cookieHeader := req.Header.Get("Cookie")
	if cookieHeader == "" {
		t.Error("Expected Cookie header to be set, but it was empty")
	}

	// Check that auth token is in cookies
	if !strings.Contains(cookieHeader, "token="+authToken) {
		t.Errorf("Expected cookie to contain 'token=%s', got '%s'", authToken, cookieHeader)
	}
}

func TestSetCookiesFromString(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	validator := NewAPISpecValidator("http://localhost:8080", logger.Sugar())

	// Test cookie string similar to the curl request
	cookieString := "_ga=GA1.1.654831891.1739442610; _ga_5WWMF8TQVE=GS1.1.1742452726.1.1.1742452747.0.0.0; session_id=abc123"
	validator.SetCookiesFromString(cookieString)
	// Create a test request
	req, err := http.NewRequest("GET", "http://localhost:8080/test", nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	// Apply cookies directly to test cookie parsing
	validator.BuildAndSetCookies(req)

	cookieHeader := req.Header.Get("Cookie")
	if cookieHeader == "" {
		t.Error("Expected Cookie header to be set, but it was empty")
	}

	// Check that parsed cookies are included
	if !strings.Contains(cookieHeader, "_ga=GA1.1.654831891.1739442610") {
		t.Errorf("Expected cookie to contain '_ga=GA1.1.654831891.1739442610', got '%s'", cookieHeader)
	}

	if !strings.Contains(cookieHeader, "session_id=abc123") {
		t.Errorf("Expected cookie to contain 'session_id=abc123', got '%s'", cookieHeader)
	}
}

func TestCookieOnlyAuthentication(t *testing.T) {
	logger, _ := zap.NewDevelopment()
	validator := NewAPISpecValidator("http://localhost:8080", logger.Sugar())

	authToken := "test-auth-token"
	validator.SetToken(authToken)

	// Test that only cookies are set, no Authorization header
	req, _ := http.NewRequest("GET", "http://localhost:8080/test", nil)
	validator.setAuthentication(req)

	authHeader := req.Header.Get("Authorization")
	if authHeader != "" {
		t.Errorf("Expected no Authorization header in cookie-only mode, got '%s'", authHeader)
	}

	cookieHeader := req.Header.Get("Cookie")
	if !strings.Contains(cookieHeader, "token="+authToken) {
		t.Errorf("Expected cookie to contain 'token=%s', got '%s'", authToken, cookieHeader)
	}
}
