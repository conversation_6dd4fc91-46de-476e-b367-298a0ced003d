# API Spec Validation Report

Generated: 2025-08-04T17:07:35+05:30

## Summary

- Total Endpoints: 241
- Passed: 0
- Failed: 241
- Warnings: 0
- Success Rate: 0.00%

## Detailed Results

### ❌ POST /config

- **Status**: FAIL
- **Duration**: 428.991792ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ PUT /config

- **Status**: FAIL
- **Duration**: 104.09175ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /gitops/config

- **Status**: FAIL
- **Duration**: 96.566042ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /gitops/config-by-provider

- **Status**: FAIL
- **Duration**: 97.703334ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ GET /gitops/config/{id}

- **Status**: FAIL
- **Duration**: 120.175125ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ GET /gitops/configured

- **Status**: FAIL
- **Duration**: 95.018334ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /validate

- **Status**: FAIL
- **Duration**: 103.946083ms
- **Spec File**: ../../specs/gitops/validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/app/labels/list

- **Status**: FAIL
- **Duration**: 108.124041ms
- **Spec File**: ../../specs/application/labels.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/meta/info/{appId}

- **Status**: FAIL
- **Duration**: 100.034834ms
- **Spec File**: ../../specs/application/labels.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/helm/meta/info/{appId}

- **Status**: FAIL
- **Duration**: 110.118125ms
- **Spec File**: ../../specs/application/labels.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/ci-pipeline/{ciPipelineId}/linked-ci/downstream/cd

- **Status**: FAIL
- **Duration**: 95.152334ms
- **Spec File**: ../../specs/ci-pipeline/ciPipelineDownstream/downstream-linked-ci-view-spec.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/ci-pipeline/{ciPipelineId}/linked-ci/downstream/env

- **Status**: FAIL
- **Duration**: 94.320833ms
- **Spec File**: ../../specs/ci-pipeline/ciPipelineDownstream/downstream-linked-ci-view-spec.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/team/delete

- **Status**: FAIL
- **Duration**: 96.069791ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/docker/registry/delete

- **Status**: FAIL
- **Duration**: 101.934167ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/env/delete

- **Status**: FAIL
- **Duration**: 98.320125ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/git/provider/delete

- **Status**: FAIL
- **Duration**: 186.826209ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/app/material/delete

- **Status**: FAIL
- **Duration**: 99.011458ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/app-store/repo/delete

- **Status**: FAIL
- **Duration**: 145.256333ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/chart-group/delete

- **Status**: FAIL
- **Duration**: 107.5385ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/cluster/delete

- **Status**: FAIL
- **Duration**: 118.241791ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/notification/channel/delete

- **Status**: FAIL
- **Duration**: 113.4875ms
- **Spec File**: ../../specs/common/delete-options.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/app/cd-pipeline/patch/deployment

- **Status**: FAIL
- **Duration**: 99.212583ms
- **Spec File**: ../../specs/deployment/app-type-change.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /deployment/pipeline/rollback

- **Status**: FAIL
- **Duration**: 104.002292ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /deployment/pipeline/trigger

- **Status**: FAIL
- **Duration**: 100.193125ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /deployment/pipeline/configure

- **Status**: FAIL
- **Duration**: 95.801792ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /deployment/pipeline/history

- **Status**: FAIL
- **Duration**: 103.749417ms
- **Spec File**: ../../specs/deployment/pipeline.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/app/deployment-configuration/all/latest/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 96.469125ms
- **Spec File**: ../../specs/deployment/rollback.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/app/history/deployed-configuration/all/latest/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 103.014667ms
- **Spec File**: ../../specs/deployment/rollback.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/history/deployed-configuration/all/{appId}/{pipelineId}/{wfrId}

- **Status**: FAIL
- **Duration**: 102.594667ms
- **Spec File**: ../../specs/deployment/rollback.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrtor/batch/v1beta1/cd-pipeline

- **Status**: FAIL
- **Duration**: 119.687292ms
- **Spec File**: ../../specs/environment/bulk-delete.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ PATCH /orchestrator/app/env/patch

- **Status**: FAIL
- **Duration**: 121.014875ms
- **Spec File**: ../../specs/helm/deployment-chart-type.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/config/manifest

- **Status**: FAIL
- **Duration**: 123.658625ms
- **Spec File**: ../../specs/environment/config-diff.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/config/autocomplete

- **Status**: FAIL
- **Duration**: 94.388375ms
- **Spec File**: ../../specs/environment/config-diff.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/config/compare/{resource}

- **Status**: FAIL
- **Duration**: 152.564542ms
- **Spec File**: ../../specs/environment/config-diff.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/config/data

- **Status**: FAIL
- **Duration**: 102.77525ms
- **Spec File**: ../../specs/environment/config-diff.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /env/namespace/autocomplete

- **Status**: FAIL
- **Duration**: 103.886333ms
- **Spec File**: ../../specs/environment/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/external-links/tools

- **Status**: FAIL
- **Duration**: 110.587917ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/external-links

- **Status**: FAIL
- **Duration**: 109.094417ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/external-links

- **Status**: FAIL
- **Duration**: 105.956417ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/external-links

- **Status**: FAIL
- **Duration**: 100.503666ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/external-links

- **Status**: FAIL
- **Duration**: 94.277209ms
- **Spec File**: ../../specs/external-links/external-links-specs.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/gitops/config-by-provider

- **Status**: FAIL
- **Duration**: 121.566ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/gitops/config/{id}

- **Status**: FAIL
- **Duration**: 122.115708ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/gitops/configured

- **Status**: FAIL
- **Duration**: 101.984042ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/gitops/validate

- **Status**: FAIL
- **Duration**: 103.723ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/gitops/config

- **Status**: FAIL
- **Duration**: 96.177708ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/gitops/config

- **Status**: FAIL
- **Duration**: 96.138791ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/gitops/config

- **Status**: FAIL
- **Duration**: 102.840625ms
- **Spec File**: ../../specs/gitops/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/app/deployment/template/data

- **Status**: FAIL
- **Duration**: 119.034167ms
- **Spec File**: ../../specs/gitops/manifest-generation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/app/deployments/{app-id}/{env-id}

- **Status**: FAIL
- **Duration**: 96.021958ms
- **Spec File**: ../../specs/gitops/manifest-generation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/git/host/webhook-meta-config/{gitProviderId}

- **Status**: FAIL
- **Duration**: 95.556875ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/git/host/{id}

- **Status**: FAIL
- **Duration**: 147.766084ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/git/host/{id}/event

- **Status**: FAIL
- **Duration**: 104.434917ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/git/host

- **Status**: FAIL
- **Duration**: 101.402042ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/git/host

- **Status**: FAIL
- **Duration**: 112.502667ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/git/host/event/{eventId}

- **Status**: FAIL
- **Duration**: 98.588333ms
- **Spec File**: ../../specs/gitops/submodules.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /bulk/v1beta1/application

- **Status**: FAIL
- **Duration**: 259.918042ms
- **Spec File**: ../../specs/jobs/batch.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /bulk/v1beta1/application/dryrun

- **Status**: FAIL
- **Duration**: 136.987875ms
- **Spec File**: ../../specs/jobs/batch.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /operate

- **Status**: FAIL
- **Duration**: 99.498875ms
- **Spec File**: ../../specs/jobs/batch.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /app/edit/projects

- **Status**: FAIL
- **Duration**: 105.954916ms
- **Spec File**: ../../specs/application/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ POST /app/list

- **Status**: FAIL
- **Duration**: 102.452625ms
- **Spec File**: ../../specs/application/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /core/v1beta1/application

- **Status**: FAIL
- **Duration**: 103.618583ms
- **Spec File**: ../../specs/application/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /app/details/{appId}

- **Status**: FAIL
- **Duration**: 116.142792ms
- **Spec File**: ../../specs/application/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ POST /app/edit

- **Status**: FAIL
- **Duration**: 97.067625ms
- **Spec File**: ../../specs/application/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ GET /orchestrator/flux-application

- **Status**: FAIL
- **Duration**: 118.616625ms
- **Spec File**: ../../specs/gitops/fluxcd.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/flux-application/app

- **Status**: FAIL
- **Duration**: 140.65625ms
- **Spec File**: ../../specs/gitops/fluxcd.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/cluster/access/{id}

- **Status**: FAIL
- **Duration**: 119.313583ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character 'p' after top-level value

---

### ❌ DELETE /orchestrator/cluster/access/{id}

- **Status**: FAIL
- **Duration**: 100.859792ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character 'p' after top-level value

---

### ❌ POST /orchestrator/cluster/access

- **Status**: FAIL
- **Duration**: 151.120792ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ PUT /orchestrator/cluster/access

- **Status**: FAIL
- **Duration**: 104.946ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/cluster/access/list

- **Status**: FAIL
- **Duration**: 104.012667ms
- **Spec File**: ../../specs/kubernetes/access-policy.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/k8s/capacity/cluster/{clusterId}

- **Status**: FAIL
- **Duration**: 105.232667ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/k8s/capacity/node

- **Status**: FAIL
- **Duration**: 114.064292ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/k8s/capacity/node

- **Status**: FAIL
- **Duration**: 98.893041ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/k8s/capacity/node

- **Status**: FAIL
- **Duration**: 105.797167ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/k8s/capacity/node/cordon

- **Status**: FAIL
- **Duration**: 98.172541ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/k8s/capacity/node/drain

- **Status**: FAIL
- **Duration**: 97.900792ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/k8s/capacity/node/list

- **Status**: FAIL
- **Duration**: 99.076542ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/k8s/capacity/node/taints/edit

- **Status**: FAIL
- **Duration**: 125.879917ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/k8s/capacity/cluster/list

- **Status**: FAIL
- **Duration**: 98.86225ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/k8s/capacity/cluster/list/raw

- **Status**: FAIL
- **Duration**: 99.662ms
- **Spec File**: ../../specs/kubernetes/capacity.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/k8s/resources/ephemeralContainers

- **Status**: FAIL
- **Duration**: 104.287167ms
- **Spec File**: ../../specs/kubernetes/ephemeral-containers.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/k8s/resources/ephemeralContainers

- **Status**: FAIL
- **Duration**: 97.660208ms
- **Spec File**: ../../specs/kubernetes/ephemeral-containers.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/application/template-chart

- **Status**: FAIL
- **Duration**: 98.083125ms
- **Spec File**: ../../specs/openapiClient/api/openapi.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/application/rollback

- **Status**: FAIL
- **Duration**: 102.596167ms
- **Spec File**: ../../specs/openapiClient/api/openapi.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/configmap/global

- **Status**: FAIL
- **Duration**: 97.988333ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/configmap/bulk/patch

- **Status**: FAIL
- **Duration**: 108.226667ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/configmap/environment/{appId}/{envId}/{id}

- **Status**: FAIL
- **Duration**: 115.956084ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/configmap/global/{appId}/{id}

- **Status**: FAIL
- **Duration**: 101.836666ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/configmap/environment

- **Status**: FAIL
- **Duration**: 113.001958ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/environment/{appId}/{envId}

- **Status**: FAIL
- **Duration**: 93.756958ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/global/edit/{appId}/{id}

- **Status**: FAIL
- **Duration**: 98.723792ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/global/{appId}

- **Status**: FAIL
- **Duration**: 102.767167ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/configmap/environment/edit/{appId}/{envId}/{id}

- **Status**: FAIL
- **Duration**: 97.223709ms
- **Spec File**: ../../specs/plugins/config-maps.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/user

- **Status**: FAIL
- **Duration**: 108.737959ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/user

- **Status**: FAIL
- **Duration**: 110.116167ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/user/bulk

- **Status**: FAIL
- **Duration**: 104.787959ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/v2

- **Status**: FAIL
- **Duration**: 119.073583ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/user/{id}

- **Status**: FAIL
- **Duration**: 95.583708ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/{id}

- **Status**: FAIL
- **Duration**: 107.527875ms
- **Spec File**: ../../specs/security/user-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /app-store/discover/application/{appStoreId}/version/autocomplete

- **Status**: FAIL
- **Duration**: 101.733709ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /app-store/discover/application/{id}

- **Status**: FAIL
- **Duration**: 94.596083ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /app-store/discover/search

- **Status**: FAIL
- **Duration**: 97.804542ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /app-store/discover

- **Status**: FAIL
- **Duration**: 97.838ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /app-store/discover/application/chartInfo/{appStoreApplicationVersionId}

- **Status**: FAIL
- **Duration**: 104.864958ms
- **Spec File**: ../../specs/app-store.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /app/commit-info/{ciPipelineMaterialId}/{gitHash}

- **Status**: FAIL
- **Duration**: 104.582792ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-build-spec.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ POST /app/workflow/trigger/{pipelineId}

- **Status**: FAIL
- **Duration**: 95.121541ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-build-spec.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /app/workflow/{workflowId}

- **Status**: FAIL
- **Duration**: 97.314125ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-build-spec.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404
- **RESPONSE_FORMAT_ERROR**: Response is not valid JSON: invalid character '<' looking for beginning of value

---

### ❌ GET /orchestrator/version

- **Status**: FAIL
- **Duration**: 109.331625ms
- **Spec File**: ../../specs/common/version.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /create

- **Status**: FAIL
- **Duration**: 98.37525ms
- **Spec File**: ../../specs/helm/repo-validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /update

- **Status**: FAIL
- **Duration**: 99.956042ms
- **Spec File**: ../../specs/helm/repo-validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /validate

- **Status**: FAIL
- **Duration**: 96.010625ms
- **Spec File**: ../../specs/helm/repo-validation.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/app/ci-pipeline/{appId}

- **Status**: FAIL
- **Duration**: 118.223917ms
- **Spec File**: ../../specs/infrastructure/docker-build.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/wf/all/component-names/{appId}

- **Status**: FAIL
- **Duration**: 110.590417ms
- **Spec File**: ../../specs/infrastructure/docker-build.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/app/ci-pipeline/patch

- **Status**: FAIL
- **Duration**: 110.993708ms
- **Spec File**: ../../specs/infrastructure/docker-build.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /v1beta1/deploy

- **Status**: FAIL
- **Duration**: 121.474917ms
- **Spec File**: ../../specs/jobs/bulk-actions.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /v1beta1/hibernate

- **Status**: FAIL
- **Duration**: 121.995084ms
- **Spec File**: ../../specs/jobs/bulk-actions.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /v1beta1/unhibernate

- **Status**: FAIL
- **Duration**: 100.043292ms
- **Spec File**: ../../specs/jobs/bulk-actions.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/resource/create

- **Status**: FAIL
- **Duration**: 101.299208ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/resource/delete

- **Status**: FAIL
- **Duration**: 115.88125ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/resource/inception/info

- **Status**: FAIL
- **Duration**: 118.561ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/resource/update

- **Status**: FAIL
- **Duration**: 100.46075ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/resource/urls

- **Status**: FAIL
- **Duration**: 122.834333ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/api-resources/{clusterId}

- **Status**: FAIL
- **Duration**: 121.072291ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/resource

- **Status**: FAIL
- **Duration**: 115.14525ms
- **Spec File**: ../../specs/kubernetes/resources.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/module

- **Status**: FAIL
- **Duration**: 95.980417ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/module

- **Status**: FAIL
- **Duration**: 108.811125ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/server

- **Status**: FAIL
- **Duration**: 99.60025ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/server

- **Status**: FAIL
- **Duration**: 100.068792ms
- **Spec File**: ../../specs/modularisation/v1.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/deployment-status/timeline/{appId}/{envId}

- **Status**: FAIL
- **Duration**: 95.368333ms
- **Spec File**: ../../specs/deployment/timeline.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/chart-repo/list

- **Status**: FAIL
- **Duration**: 99.841541ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/chart-repo/validate

- **Status**: FAIL
- **Duration**: 114.610625ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app-store/chart-provider/list

- **Status**: FAIL
- **Duration**: 99.854584ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/app-store/chart-provider/update

- **Status**: FAIL
- **Duration**: 104.965209ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/app-store/chart-provider/sync-chart

- **Status**: FAIL
- **Duration**: 123.923041ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/chart-repo/{id}

- **Status**: FAIL
- **Duration**: 275.613292ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/chart-group

- **Status**: FAIL
- **Duration**: 95.9475ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/chart-group

- **Status**: FAIL
- **Duration**: 105.927375ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/chart-repo

- **Status**: FAIL
- **Duration**: 102.333084ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 405

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 405

---

### ❌ PUT /orchestrator/chart-repo

- **Status**: FAIL
- **Duration**: 129.534125ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 405

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 405

---

### ❌ POST /orchestrator/chart-repo/sync

- **Status**: FAIL
- **Duration**: 106.118209ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/chart-group/entries

- **Status**: FAIL
- **Duration**: 101.919333ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/chart-group/list

- **Status**: FAIL
- **Duration**: 111.823583ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/chart-group/{id}

- **Status**: FAIL
- **Duration**: 113.310542ms
- **Spec File**: ../../specs/helm/provider.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/k8s/resource/list

- **Status**: FAIL
- **Duration**: 100.764167ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/k8s/api-resources/{clusterId}

- **Status**: FAIL
- **Duration**: 120.723625ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/k8s/events

- **Status**: FAIL
- **Duration**: 112.003042ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}

- **Status**: FAIL
- **Duration**: 95.965083ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/k8s/pods/logs/{podName}

- **Status**: FAIL
- **Duration**: 104.273875ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/k8s/resource

- **Status**: FAIL
- **Duration**: 95.041375ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/k8s/resource

- **Status**: FAIL
- **Duration**: 100.010666ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/k8s/resource/create

- **Status**: FAIL
- **Duration**: 101.584166ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/k8s/resource/delete

- **Status**: FAIL
- **Duration**: 104.306209ms
- **Spec File**: ../../specs/kubernetes/apis.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/{id}

- **Status**: FAIL
- **Duration**: 99.823167ms
- **Spec File**: ../../specs/security/policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user

- **Status**: FAIL
- **Duration**: 119.299125ms
- **Spec File**: ../../specs/security/policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/user

- **Status**: FAIL
- **Duration**: 120.959875ms
- **Spec File**: ../../specs/security/policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/user

- **Status**: FAIL
- **Duration**: 120.09275ms
- **Spec File**: ../../specs/security/policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/user/bulk

- **Status**: FAIL
- **Duration**: 129.870375ms
- **Spec File**: ../../specs/security/policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/v2

- **Status**: FAIL
- **Duration**: 109.4035ms
- **Spec File**: ../../specs/security/policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app-store/installed-app

- **Status**: FAIL
- **Duration**: 124.228833ms
- **Spec File**: ../../specs/helm/charts.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app-store/installed-app/notes/{installed-app-id}/{env-id}

- **Status**: FAIL
- **Duration**: 102.990084ms
- **Spec File**: ../../specs/helm/charts.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ PATCH /orchestrator/app/ci-pipeline/patch-source

- **Status**: FAIL
- **Duration**: 112.479375ms
- **Spec File**: ../../specs/ci-pipeline/ci-pipeline-change-source.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/deployment/template/fetch

- **Status**: FAIL
- **Duration**: 125.182792ms
- **Spec File**: ../../specs/deployment/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/deployment/template/upload

- **Status**: FAIL
- **Duration**: 104.680792ms
- **Spec File**: ../../specs/deployment/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/deployment/template/validate

- **Status**: FAIL
- **Duration**: 101.305167ms
- **Spec File**: ../../specs/deployment/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/webhook/notification/{id}

- **Status**: FAIL
- **Duration**: 111.546666ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/webhook/ci/workflow

- **Status**: FAIL
- **Duration**: 117.360958ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/webhook/ext-ci/{externalCiId}

- **Status**: FAIL
- **Duration**: 120.404458ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/webhook/git

- **Status**: FAIL
- **Duration**: 125.429375ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/webhook/git/{gitHostId}

- **Status**: FAIL
- **Duration**: 103.985125ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/webhook/git/{gitHostId}/{secret}

- **Status**: FAIL
- **Duration**: 113.151375ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/webhook/notification

- **Status**: FAIL
- **Duration**: 123.2335ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/webhook/notification/variables

- **Status**: FAIL
- **Duration**: 112.140166ms
- **Spec File**: ../../specs/notifications/webhooks.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/plugin/global/list/v2/min

- **Status**: FAIL
- **Duration**: 101.499708ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/plugin/global/list/tags

- **Status**: FAIL
- **Duration**: 128.225292ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/plugin/global/create

- **Status**: FAIL
- **Duration**: 112.434417ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/plugin/global/list/detail/v2

- **Status**: FAIL
- **Duration**: 103.36225ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/plugin/global/list/global-variable

- **Status**: FAIL
- **Duration**: 94.944667ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/plugin/global/list/v2

- **Status**: FAIL
- **Duration**: 102.836417ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/plugin/global/migrate

- **Status**: FAIL
- **Duration**: 104.527375ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/plugin/global/detail/all

- **Status**: FAIL
- **Duration**: 112.586542ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/plugin/global/detail/{pluginId}

- **Status**: FAIL
- **Duration**: 97.4165ms
- **Spec File**: ../../specs/plugins/global.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/user/resource/options/{kind}/{version}

- **Status**: FAIL
- **Duration**: 110.566583ms
- **Spec File**: ../../specs/userResource/userResource.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ GET /orchestrator/cluster/auth-list

- **Status**: FAIL
- **Duration**: 116.308959ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/cluster/namespaces

- **Status**: FAIL
- **Duration**: 95.715917ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/cluster/namespaces/{clusterId}

- **Status**: FAIL
- **Duration**: 99.93775ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/cluster/saveClusters

- **Status**: FAIL
- **Duration**: 96.314292ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/cluster/validate

- **Status**: FAIL
- **Duration**: 112.022125ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/cluster

- **Status**: FAIL
- **Duration**: 118.335042ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/cluster

- **Status**: FAIL
- **Duration**: 117.872708ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/cluster

- **Status**: FAIL
- **Duration**: 114.336167ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/cluster

- **Status**: FAIL
- **Duration**: 99.794041ms
- **Spec File**: ../../specs/kubernetes/cluster-management.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/cluster

- **Status**: FAIL
- **Duration**: 96.009166ms
- **Spec File**: ../../specs/kubernetes/cluster.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/cluster

- **Status**: FAIL
- **Duration**: 100.014708ms
- **Spec File**: ../../specs/kubernetes/cluster.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/cluster/auth-list

- **Status**: FAIL
- **Duration**: 110.709625ms
- **Spec File**: ../../specs/kubernetes/cluster.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/notification/recipient

- **Status**: FAIL
- **Duration**: 118.232292ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ DELETE /orchestrator/notification

- **Status**: FAIL
- **Duration**: 117.317458ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/notification

- **Status**: FAIL
- **Duration**: 125.796458ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /orchestrator/notification

- **Status**: FAIL
- **Duration**: 104.678125ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/notification

- **Status**: FAIL
- **Duration**: 109.383125ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/notification/channel

- **Status**: FAIL
- **Duration**: 107.288042ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/notification/channel

- **Status**: FAIL
- **Duration**: 94.505667ms
- **Spec File**: ../../specs/notifications/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/api-token

- **Status**: FAIL
- **Duration**: 96.4125ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/api-token

- **Status**: FAIL
- **Duration**: 114.919125ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/api-token/webhook

- **Status**: FAIL
- **Duration**: 109.682542ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/api-token/{id}

- **Status**: FAIL
- **Duration**: 102.200417ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/api-token/{id}

- **Status**: FAIL
- **Duration**: 116.360625ms
- **Spec File**: ../../specs/openapiClient/api/apiToken_api-openapi.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/user/bulk

- **Status**: FAIL
- **Duration**: 117.823417ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/user/role/group

- **Status**: FAIL
- **Duration**: 119.832041ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/user/role/group

- **Status**: FAIL
- **Duration**: 124.018333ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/user/role/group/bulk

- **Status**: FAIL
- **Duration**: 108.281333ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/role/group/v2

- **Status**: FAIL
- **Duration**: 115.138125ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/v2

- **Status**: FAIL
- **Duration**: 120.257ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/api-token

- **Status**: FAIL
- **Duration**: 116.059917ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/api-token

- **Status**: FAIL
- **Duration**: 116.574958ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/api-token/{id}

- **Status**: FAIL
- **Duration**: 272.02375ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/api-token/{id}

- **Status**: FAIL
- **Duration**: 128.355458ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/user

- **Status**: FAIL
- **Duration**: 97.800542ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/user

- **Status**: FAIL
- **Duration**: 97.766ms
- **Spec File**: ../../specs/security/core.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/role/group

- **Status**: FAIL
- **Duration**: 102.2035ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/user/role/group

- **Status**: FAIL
- **Duration**: 97.495292ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/user/role/group

- **Status**: FAIL
- **Duration**: 109.327625ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/user/role/group/bulk

- **Status**: FAIL
- **Duration**: 121.784292ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/role/group/detailed/get

- **Status**: FAIL
- **Duration**: 119.967833ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/role/group/search

- **Status**: FAIL
- **Duration**: 108.21925ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/role/group/v2

- **Status**: FAIL
- **Duration**: 108.4275ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ POST /orchestrator/user/role/group/v2

- **Status**: FAIL
- **Duration**: 118.388417ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ PUT /orchestrator/user/role/group/v2

- **Status**: FAIL
- **Duration**: 121.864333ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ DELETE /orchestrator/user/role/group/{id}

- **Status**: FAIL
- **Duration**: 105.422959ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/user/role/group/{id}

- **Status**: FAIL
- **Duration**: 111.855792ms
- **Spec File**: ../../specs/security/group-policy.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/history/deployed-component/detail/{appId}/{pipelineId}/{id}

- **Status**: FAIL
- **Duration**: 118.804959ms
- **Spec File**: ../../specs/audit/api-changes.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/history/deployed-component/list/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 105.543375ms
- **Spec File**: ../../specs/audit/api-changes.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/history/deployed-configuration/{appId}/{pipelineId}/{wfrId}

- **Status**: FAIL
- **Duration**: 106.52325ms
- **Spec File**: ../../specs/audit/api-changes.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/history/deployed-configuration/{appId}/{pipelineId}/{wfrId}

- **Status**: FAIL
- **Duration**: 97.517625ms
- **Spec File**: ../../specs/audit/definitions.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/history/deployed-component/detail/{appId}/{pipelineId}/{id}

- **Status**: FAIL
- **Duration**: 100.271542ms
- **Spec File**: ../../specs/audit/definitions.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/history/deployed-component/list/{appId}/{pipelineId}

- **Status**: FAIL
- **Duration**: 109.198125ms
- **Spec File**: ../../specs/audit/definitions.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/app/template/default/{appId}/{chartRefId}

- **Status**: FAIL
- **Duration**: 119.136042ms
- **Spec File**: ../../specs/environment/templates.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /orchestrator/chartref/autocomplete/{appId}

- **Status**: FAIL
- **Duration**: 96.374083ms
- **Spec File**: ../../specs/helm/dynamic-charts.yaml
- **Response Code**: 401

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 401

---

### ❌ GET /job/ci-pipeline/list/{jobId}

- **Status**: FAIL
- **Duration**: 109.883792ms
- **Spec File**: ../../specs/jobs/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /job/list

- **Status**: FAIL
- **Duration**: 121.375084ms
- **Spec File**: ../../specs/jobs/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

### ❌ POST /job

- **Status**: FAIL
- **Duration**: 119.032125ms
- **Spec File**: ../../specs/jobs/core.yaml
- **Response Code**: 404

**Issues:**
- **STATUS_CODE_MISMATCH**: Expected status 200, got 404

---

