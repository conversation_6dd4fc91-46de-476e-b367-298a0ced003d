/*
 * Copyright (c) 2020-2024. Devtron Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package models

/*
func TestDuplicateHelmValuesSave(t *testing.T) {

	hv := &HelmValues{
		AppName:           "demo-app-5",
		TargetEnvironment: "prod-env",
		Values: `{
    "image": {
      "tag": "1.2.0",
      "image":"nginx"
    }
  }`,

		UpdatedBy: 2,
		UpdatedOn: time.Now(),
		CreatedOn: time.Now(),
		CreatedBy: 3,

		Active: true,
	}

	err := AddHelmValues(hv)
	err1 := AddHelmValues(hv)
	assert.NoError(t, err)
	assert.Error(t, err1)
}

func TestGetHelmValues(t *testing.T) {
	hv, err := GetHelmValues("demo-app", "test-env-1")
	assert.NoError(t, err)
	assert.NotNil(t, hv)

}

func TestCreate(t *testing.T) {
	err := createAuthor()
	assert.NoError(t, err)
}
*/
