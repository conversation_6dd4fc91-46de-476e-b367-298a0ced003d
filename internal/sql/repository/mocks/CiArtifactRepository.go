// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import (
	bean "github.com/devtron-labs/devtron/api/bean"
	mock "github.com/stretchr/testify/mock"

	repository "github.com/devtron-labs/devtron/internal/sql/repository"
)

// CiArtifactRepository is an autogenerated mock type for the CiArtifactRepository type
type CiArtifactRepository struct {
	mock.Mock
}

// Delete provides a mock function with given fields: artifact
func (_m *CiArtifactRepository) Delete(artifact *repository.CiArtifact) error {
	ret := _m.Called(artifact)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.CiArtifact) error); ok {
		r0 = rf(artifact)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FinDByParentCiArtifactAndCiId provides a mock function with given fields: parentCiArtifact, ciPipelineIds
func (_m *CiArtifactRepository) FinDByParentCiArtifactAndCiId(parentCiArtifact int, ciPipelineIds []int) ([]*repository.CiArtifact, error) {
	ret := _m.Called(parentCiArtifact, ciPipelineIds)

	var r0 []*repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int, []int) ([]*repository.CiArtifact, error)); ok {
		return rf(parentCiArtifact, ciPipelineIds)
	}
	if rf, ok := ret.Get(0).(func(int, []int) []*repository.CiArtifact); ok {
		r0 = rf(parentCiArtifact, ciPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(parentCiArtifact, ciPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Get provides a mock function with given fields: id
func (_m *CiArtifactRepository) Get(id int) (*repository.CiArtifact, error) {
	ret := _m.Called(id)

	var r0 *repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.CiArtifact, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.CiArtifact); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArtifactByCdWorkflowId provides a mock function with given fields: cdWorkflowId
func (_m *CiArtifactRepository) GetArtifactByCdWorkflowId(cdWorkflowId int) (*repository.CiArtifact, error) {
	ret := _m.Called(cdWorkflowId)

	var r0 *repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.CiArtifact, error)); ok {
		return rf(cdWorkflowId)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.CiArtifact); ok {
		r0 = rf(cdWorkflowId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdWorkflowId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArtifactParentCiAndWorkflowDetailsByIds provides a mock function with given fields: ids
func (_m *CiArtifactRepository) GetArtifactParentCiAndWorkflowDetailsByIds(ids []int) ([]*repository.CiArtifact, error) {
	ret := _m.Called(ids)

	var r0 []*repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.CiArtifact, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.CiArtifact); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArtifactsByCDPipeline provides a mock function with given fields: cdPipelineId, limit, parentId, parentType
func (_m *CiArtifactRepository) GetArtifactsByCDPipeline(cdPipelineId int, limit int, parentId int, parentType bean.WorkflowType) ([]repository.CiArtifact, error) {
	ret := _m.Called(cdPipelineId, limit, parentId, parentType)

	var r0 []repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int, int, bean.WorkflowType) ([]repository.CiArtifact, error)); ok {
		return rf(cdPipelineId, limit, parentId, parentType)
	}
	if rf, ok := ret.Get(0).(func(int, int, int, bean.WorkflowType) []repository.CiArtifact); ok {
		r0 = rf(cdPipelineId, limit, parentId, parentType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int, int, bean.WorkflowType) error); ok {
		r1 = rf(cdPipelineId, limit, parentId, parentType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArtifactsByCDPipelineAndRunnerType provides a mock function with given fields: cdPipelineId, runnerType
func (_m *CiArtifactRepository) GetArtifactsByCDPipelineAndRunnerType(cdPipelineId int, runnerType bean.WorkflowType) ([]repository.CiArtifact, error) {
	ret := _m.Called(cdPipelineId, runnerType)

	var r0 []repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int, bean.WorkflowType) ([]repository.CiArtifact, error)); ok {
		return rf(cdPipelineId, runnerType)
	}
	if rf, ok := ret.Get(0).(func(int, bean.WorkflowType) []repository.CiArtifact); ok {
		r0 = rf(cdPipelineId, runnerType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int, bean.WorkflowType) error); ok {
		r1 = rf(cdPipelineId, runnerType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArtifactsByCDPipelineV2 provides a mock function with given fields: cdPipelineId
func (_m *CiArtifactRepository) GetArtifactsByCDPipelineV2(cdPipelineId int) ([]repository.CiArtifact, error) {
	ret := _m.Called(cdPipelineId)

	var r0 []repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]repository.CiArtifact, error)); ok {
		return rf(cdPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) []repository.CiArtifact); ok {
		r0 = rf(cdPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetArtifactsByCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *CiArtifactRepository) GetArtifactsByCiPipelineId(ciPipelineId int) ([]repository.CiArtifact, error) {
	ret := _m.Called(ciPipelineId)

	var r0 []repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]repository.CiArtifact, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) []repository.CiArtifact); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByIds provides a mock function with given fields: ids
func (_m *CiArtifactRepository) GetByIds(ids []int) ([]*repository.CiArtifact, error) {
	ret := _m.Called(ids)

	var r0 []*repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*repository.CiArtifact, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*repository.CiArtifact); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByImageDigest provides a mock function with given fields: imageDigest
func (_m *CiArtifactRepository) GetByImageDigest(imageDigest string) (*repository.CiArtifact, error) {
	ret := _m.Called(imageDigest)

	var r0 *repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*repository.CiArtifact, error)); ok {
		return rf(imageDigest)
	}
	if rf, ok := ret.Get(0).(func(string) *repository.CiArtifact); ok {
		r0 = rf(imageDigest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(imageDigest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetByWfId provides a mock function with given fields: wfId
func (_m *CiArtifactRepository) GetByWfId(wfId int) (*repository.CiArtifact, error) {
	ret := _m.Called(wfId)

	var r0 *repository.CiArtifact
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*repository.CiArtifact, error)); ok {
		return rf(wfId)
	}
	if rf, ok := ret.Get(0).(func(int) *repository.CiArtifact); ok {
		r0 = rf(wfId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*repository.CiArtifact)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(wfId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLatest provides a mock function with given fields: cdPipelineId
func (_m *CiArtifactRepository) GetLatest(cdPipelineId int) (int, error) {
	ret := _m.Called(cdPipelineId)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(cdPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(cdPipelineId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(cdPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: artifact
func (_m *CiArtifactRepository) Save(artifact *repository.CiArtifact) error {
	ret := _m.Called(artifact)

	var r0 error
	if rf, ok := ret.Get(0).(func(*repository.CiArtifact) error); ok {
		r0 = rf(artifact)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveAll provides a mock function with given fields: artifacts
func (_m *CiArtifactRepository) SaveAll(artifacts []*repository.CiArtifact) error {
	ret := _m.Called(artifacts)

	var r0 error
	if rf, ok := ret.Get(0).(func([]*repository.CiArtifact) error); ok {
		r0 = rf(artifacts)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewCiArtifactRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewCiArtifactRepository creates a new instance of CiArtifactRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCiArtifactRepository(t mockConstructorTestingTNewCiArtifactRepository) *CiArtifactRepository {
	mock := &CiArtifactRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
