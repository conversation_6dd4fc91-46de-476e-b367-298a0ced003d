// Code generated by mockery v2.42.0. DO NOT EDIT.

package mocks

import (
	context "context"

	ciPipeline "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig/bean/ciPipeline"

	mock "github.com/stretchr/testify/mock"

	pagination "github.com/devtron-labs/devtron/util/response/pagination"

	pg "github.com/go-pg/pg"

	pipelineConfig "github.com/devtron-labs/devtron/internal/sql/repository/pipelineConfig"
)

// CiPipelineRepository is an autogenerated mock type for the CiPipelineRepository type
type CiPipelineRepository struct {
	mock.Mock
}

// CheckIfPipelineExistsByNameAndAppId provides a mock function with given fields: pipelineName, appId
func (_m *CiPipelineRepository) CheckIfPipelineExistsByNameAndAppId(pipelineName string, appId int) (bool, error) {
	ret := _m.Called(pipelineName, appId)

	if len(ret) == 0 {
		panic("no return value specified for CheckIfPipelineExistsByNameAndAppId")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, int) (bool, error)); ok {
		return rf(pipelineName, appId)
	}
	if rf, ok := ret.Get(0).(func(string, int) bool); ok {
		r0 = rf(pipelineName, appId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, int) error); ok {
		r1 = rf(pipelineName, appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitTx provides a mock function with given fields: tx
func (_m *CiPipelineRepository) CommitTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	if len(ret) == 0 {
		panic("no return value specified for CommitTx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FetchCiPipelinesForDG provides a mock function with given fields: parentId, childCiPipelineIds
func (_m *CiPipelineRepository) FetchCiPipelinesForDG(parentId int, childCiPipelineIds []int) (*pipelineConfig.CiPipeline, int, error) {
	ret := _m.Called(parentId, childCiPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FetchCiPipelinesForDG")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(int, []int) (*pipelineConfig.CiPipeline, int, error)); ok {
		return rf(parentId, childCiPipelineIds)
	}
	if rf, ok := ret.Get(0).(func(int, []int) *pipelineConfig.CiPipeline); ok {
		r0 = rf(parentId, childCiPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []int) int); ok {
		r1 = rf(parentId, childCiPipelineIds)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(int, []int) error); ok {
		r2 = rf(parentId, childCiPipelineIds)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// FetchParentCiPipelinesForDG provides a mock function with given fields:
func (_m *CiPipelineRepository) FetchParentCiPipelinesForDG() ([]*ciPipeline.CiPipelinesMap, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FetchParentCiPipelinesForDG")
	}

	var r0 []*ciPipeline.CiPipelinesMap
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*ciPipeline.CiPipelinesMap, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*ciPipeline.CiPipelinesMap); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*ciPipeline.CiPipelinesMap)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FinDByParentCiPipelineAndAppId provides a mock function with given fields: parentCiPipeline, appIds
func (_m *CiPipelineRepository) FinDByParentCiPipelineAndAppId(parentCiPipeline int, appIds []int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(parentCiPipeline, appIds)

	if len(ret) == 0 {
		panic("no return value specified for FinDByParentCiPipelineAndAppId")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, []int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(parentCiPipeline, appIds)
	}
	if rf, ok := ret.Get(0).(func(int, []int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(parentCiPipeline, appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(parentCiPipeline, appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllDeletedPipelineCountInLast24Hour provides a mock function with given fields:
func (_m *CiPipelineRepository) FindAllDeletedPipelineCountInLast24Hour() (int, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllDeletedPipelineCountInLast24Hour")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func() (int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllPipelineCreatedCountInLast24Hour provides a mock function with given fields:
func (_m *CiPipelineRepository) FindAllPipelineCreatedCountInLast24Hour() (int, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FindAllPipelineCreatedCountInLast24Hour")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func() (int, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndProjectByCiPipelineIds provides a mock function with given fields: ciPipelineIds
func (_m *CiPipelineRepository) FindAppAndProjectByCiPipelineIds(ciPipelineIds []int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(ciPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindAppAndProjectByCiPipelineIds")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(ciPipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(ciPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppIdsForCiPipelineIds provides a mock function with given fields: pipelineIds
func (_m *CiPipelineRepository) FindAppIdsForCiPipelineIds(pipelineIds []int) (map[int]int, error) {
	ret := _m.Called(pipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindAppIdsForCiPipelineIds")
	}

	var r0 map[int]int
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) (map[int]int, error)); ok {
		return rf(pipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) map[int]int); ok {
		r0 = rf(pipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int]int)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(pipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppId provides a mock function with given fields: appId
func (_m *CiPipelineRepository) FindByAppId(appId int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(appId)

	if len(ret) == 0 {
		panic("no return value specified for FindByAppId")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByAppIds provides a mock function with given fields: appIds
func (_m *CiPipelineRepository) FindByAppIds(appIds []int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(appIds)

	if len(ret) == 0 {
		panic("no return value specified for FindByAppIds")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(appIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByCiAndAppDetailsById provides a mock function with given fields: pipelineId
func (_m *CiPipelineRepository) FindByCiAndAppDetailsById(pipelineId int) (*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(pipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindByCiAndAppDetailsById")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiPipeline, error)); ok {
		return rf(pipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiPipeline); ok {
		r0 = rf(pipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(pipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *CiPipelineRepository) FindById(id int) (*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindById")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiPipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiPipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdIncludingInActive provides a mock function with given fields: id
func (_m *CiPipelineRepository) FindByIdIncludingInActive(id int) (*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindByIdIncludingInActive")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiPipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiPipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIdsIn provides a mock function with given fields: ids
func (_m *CiPipelineRepository) FindByIdsIn(ids []int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(ids)

	if len(ret) == 0 {
		panic("no return value specified for FindByIdsIn")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByLinkedCiCount provides a mock function with given fields: parentCiPipelineId
func (_m *CiPipelineRepository) FindByLinkedCiCount(parentCiPipelineId int) (int, error) {
	ret := _m.Called(parentCiPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindByLinkedCiCount")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(parentCiPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(parentCiPipelineId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(parentCiPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByName provides a mock function with given fields: pipelineName
func (_m *CiPipelineRepository) FindByName(pipelineName string) (*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(pipelineName)

	if len(ret) == 0 {
		panic("no return value specified for FindByName")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*pipelineConfig.CiPipeline, error)); ok {
		return rf(pipelineName)
	}
	if rf, ok := ret.Get(0).(func(string) *pipelineConfig.CiPipeline); ok {
		r0 = rf(pipelineName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(pipelineName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByParentCiPipelineId provides a mock function with given fields: parentCiPipelineId
func (_m *CiPipelineRepository) FindByParentCiPipelineId(parentCiPipelineId int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(parentCiPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindByParentCiPipelineId")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(parentCiPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(parentCiPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(parentCiPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByParentCiPipelineIds provides a mock function with given fields: parentCiPipelineIds
func (_m *CiPipelineRepository) FindByParentCiPipelineIds(parentCiPipelineIds []int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(parentCiPipelineIds)

	if len(ret) == 0 {
		panic("no return value specified for FindByParentCiPipelineIds")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(parentCiPipelineIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(parentCiPipelineIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(parentCiPipelineIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByParentIdAndType provides a mock function with given fields: parentCiPipelineId, pipelineType
func (_m *CiPipelineRepository) FindByParentIdAndType(parentCiPipelineId int, pipelineType string) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(parentCiPipelineId, pipelineType)

	if len(ret) == 0 {
		panic("no return value specified for FindByParentIdAndType")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, string) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(parentCiPipelineId, pipelineType)
	}
	if rf, ok := ret.Get(0).(func(int, string) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(parentCiPipelineId, pipelineType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, string) error); ok {
		r1 = rf(parentCiPipelineId, pipelineType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiEnvMappingByCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *CiPipelineRepository) FindCiEnvMappingByCiPipelineId(ciPipelineId int) (*pipelineConfig.CiEnvMapping, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindCiEnvMappingByCiPipelineId")
	}

	var r0 *pipelineConfig.CiEnvMapping
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiEnvMapping, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiEnvMapping); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiEnvMapping)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiPipelineByAppIdAndEnvIds provides a mock function with given fields: appId, envIds
func (_m *CiPipelineRepository) FindCiPipelineByAppIdAndEnvIds(appId int, envIds []int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(appId, envIds)

	if len(ret) == 0 {
		panic("no return value specified for FindCiPipelineByAppIdAndEnvIds")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int, []int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(appId, envIds)
	}
	if rf, ok := ret.Get(0).(func(int, []int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(appId, envIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int, []int) error); ok {
		r1 = rf(appId, envIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiPipelineConfigsByIds provides a mock function with given fields: ids
func (_m *CiPipelineRepository) FindCiPipelineConfigsByIds(ids []int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(ids)

	if len(ret) == 0 {
		panic("no return value specified for FindCiPipelineConfigsByIds")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiScriptsByCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *CiPipelineRepository) FindCiScriptsByCiPipelineId(ciPipelineId int) ([]*pipelineConfig.CiPipelineScript, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindCiScriptsByCiPipelineId")
	}

	var r0 []*pipelineConfig.CiPipelineScript
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.CiPipelineScript, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.CiPipelineScript); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipelineScript)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindCiScriptsByCiPipelineIds provides a mock function with given fields: ciPipelineId
func (_m *CiPipelineRepository) FindCiScriptsByCiPipelineIds(ciPipelineId []int) ([]*pipelineConfig.CiPipelineScript, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindCiScriptsByCiPipelineIds")
	}

	var r0 []*pipelineConfig.CiPipelineScript
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.CiPipelineScript, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.CiPipelineScript); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipelineScript)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindExternalCiByAppId provides a mock function with given fields: appId
func (_m *CiPipelineRepository) FindExternalCiByAppId(appId int) ([]*pipelineConfig.ExternalCiPipeline, error) {
	ret := _m.Called(appId)

	if len(ret) == 0 {
		panic("no return value specified for FindExternalCiByAppId")
	}

	var r0 []*pipelineConfig.ExternalCiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.ExternalCiPipeline, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.ExternalCiPipeline); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.ExternalCiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindExternalCiByAppIds provides a mock function with given fields: appIds
func (_m *CiPipelineRepository) FindExternalCiByAppIds(appIds []int) ([]*pipelineConfig.ExternalCiPipeline, error) {
	ret := _m.Called(appIds)

	if len(ret) == 0 {
		panic("no return value specified for FindExternalCiByAppIds")
	}

	var r0 []*pipelineConfig.ExternalCiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*pipelineConfig.ExternalCiPipeline, error)); ok {
		return rf(appIds)
	}
	if rf, ok := ret.Get(0).(func([]int) []*pipelineConfig.ExternalCiPipeline); ok {
		r0 = rf(appIds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.ExternalCiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindExternalCiByCiPipelineId provides a mock function with given fields: ciPipelineId
func (_m *CiPipelineRepository) FindExternalCiByCiPipelineId(ciPipelineId int) (*pipelineConfig.ExternalCiPipeline, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindExternalCiByCiPipelineId")
	}

	var r0 *pipelineConfig.ExternalCiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.ExternalCiPipeline, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.ExternalCiPipeline); ok {
		r0 = rf(ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.ExternalCiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindExternalCiById provides a mock function with given fields: id
func (_m *CiPipelineRepository) FindExternalCiById(id int) (*pipelineConfig.ExternalCiPipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindExternalCiById")
	}

	var r0 *pipelineConfig.ExternalCiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.ExternalCiPipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.ExternalCiPipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.ExternalCiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindLinkedCiCount provides a mock function with given fields: ciPipelineId
func (_m *CiPipelineRepository) FindLinkedCiCount(ciPipelineId int) (int, error) {
	ret := _m.Called(ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for FindLinkedCiCount")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(ciPipelineId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindNumberOfAppsWithCiPipeline provides a mock function with given fields: appIds
func (_m *CiPipelineRepository) FindNumberOfAppsWithCiPipeline(appIds []int) (int, error) {
	ret := _m.Called(appIds)

	if len(ret) == 0 {
		panic("no return value specified for FindNumberOfAppsWithCiPipeline")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) (int, error)); ok {
		return rf(appIds)
	}
	if rf, ok := ret.Get(0).(func([]int) int); ok {
		r0 = rf(appIds)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(appIds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindOneWithAppData provides a mock function with given fields: id
func (_m *CiPipelineRepository) FindOneWithAppData(id int) (*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindOneWithAppData")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiPipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiPipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindParentCiPipelineMapByAppId provides a mock function with given fields: appId
func (_m *CiPipelineRepository) FindParentCiPipelineMapByAppId(appId int) ([]*pipelineConfig.CiPipeline, []int, error) {
	ret := _m.Called(appId)

	if len(ret) == 0 {
		panic("no return value specified for FindParentCiPipelineMapByAppId")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 []int
	var r2 error
	if rf, ok := ret.Get(0).(func(int) ([]*pipelineConfig.CiPipeline, []int, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) []int); ok {
		r1 = rf(appId)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]int)
		}
	}

	if rf, ok := ret.Get(2).(func(int) error); ok {
		r2 = rf(appId)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// FindWithMinDataByCiPipelineId provides a mock function with given fields: id
func (_m *CiPipelineRepository) FindWithMinDataByCiPipelineId(id int) (*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for FindWithMinDataByCiPipelineId")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiPipeline, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiPipeline); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCiPipelineByArtifactId provides a mock function with given fields: artifactId
func (_m *CiPipelineRepository) GetCiPipelineByArtifactId(artifactId int) (*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(artifactId)

	if len(ret) == 0 {
		panic("no return value specified for GetCiPipelineByArtifactId")
	}

	var r0 *pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.CiPipeline, error)); ok {
		return rf(artifactId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.CiPipeline); ok {
		r0 = rf(artifactId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(artifactId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDownStreamInfo provides a mock function with given fields: ctx, sourceCiPipelineId, appNameMatch, envNameMatch, req
func (_m *CiPipelineRepository) GetDownStreamInfo(ctx context.Context, sourceCiPipelineId int, appNameMatch string, envNameMatch string, req *pagination.RepositoryRequest) ([]ciPipeline.LinkedCIDetails, int, error) {
	ret := _m.Called(ctx, sourceCiPipelineId, appNameMatch, envNameMatch, req)

	if len(ret) == 0 {
		panic("no return value specified for GetDownStreamInfo")
	}

	var r0 []ciPipeline.LinkedCIDetails
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string, string, *pagination.RepositoryRequest) ([]ciPipeline.LinkedCIDetails, int, error)); ok {
		return rf(ctx, sourceCiPipelineId, appNameMatch, envNameMatch, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, string, string, *pagination.RepositoryRequest) []ciPipeline.LinkedCIDetails); ok {
		r0 = rf(ctx, sourceCiPipelineId, appNameMatch, envNameMatch, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ciPipeline.LinkedCIDetails)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, string, string, *pagination.RepositoryRequest) int); ok {
		r1 = rf(ctx, sourceCiPipelineId, appNameMatch, envNameMatch, req)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, string, string, *pagination.RepositoryRequest) error); ok {
		r2 = rf(ctx, sourceCiPipelineId, appNameMatch, envNameMatch, req)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetExternalCiPipelineByArtifactId provides a mock function with given fields: artifactId
func (_m *CiPipelineRepository) GetExternalCiPipelineByArtifactId(artifactId int) (*pipelineConfig.ExternalCiPipeline, error) {
	ret := _m.Called(artifactId)

	if len(ret) == 0 {
		panic("no return value specified for GetExternalCiPipelineByArtifactId")
	}

	var r0 *pipelineConfig.ExternalCiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*pipelineConfig.ExternalCiPipeline, error)); ok {
		return rf(artifactId)
	}
	if rf, ok := ret.Get(0).(func(int) *pipelineConfig.ExternalCiPipeline); ok {
		r0 = rf(artifactId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.ExternalCiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(artifactId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLinkedCiPipelines provides a mock function with given fields: ctx, ciPipelineId
func (_m *CiPipelineRepository) GetLinkedCiPipelines(ctx context.Context, ciPipelineId int) ([]*pipelineConfig.CiPipeline, error) {
	ret := _m.Called(ctx, ciPipelineId)

	if len(ret) == 0 {
		panic("no return value specified for GetLinkedCiPipelines")
	}

	var r0 []*pipelineConfig.CiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int) ([]*pipelineConfig.CiPipeline, error)); ok {
		return rf(ctx, ciPipelineId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int) []*pipelineConfig.CiPipeline); ok {
		r0 = rf(ctx, ciPipelineId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*pipelineConfig.CiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int) error); ok {
		r1 = rf(ctx, ciPipelineId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MarkCiPipelineScriptsInactiveByCiPipelineId provides a mock function with given fields: ciPipelineId, tx
func (_m *CiPipelineRepository) MarkCiPipelineScriptsInactiveByCiPipelineId(ciPipelineId int, tx *pg.Tx) error {
	ret := _m.Called(ciPipelineId, tx)

	if len(ret) == 0 {
		panic("no return value specified for MarkCiPipelineScriptsInactiveByCiPipelineId")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, *pg.Tx) error); ok {
		r0 = rf(ciPipelineId, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PipelineExistsByName provides a mock function with given fields: names
func (_m *CiPipelineRepository) PipelineExistsByName(names []string) ([]string, error) {
	ret := _m.Called(names)

	if len(ret) == 0 {
		panic("no return value specified for PipelineExistsByName")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]string, error)); ok {
		return rf(names)
	}
	if rf, ok := ret.Get(0).(func([]string) []string); ok {
		r0 = rf(names)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(names)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RollbackTx provides a mock function with given fields: tx
func (_m *CiPipelineRepository) RollbackTx(tx *pg.Tx) error {
	ret := _m.Called(tx)

	if len(ret) == 0 {
		panic("no return value specified for RollbackTx")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pg.Tx) error); ok {
		r0 = rf(tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Save provides a mock function with given fields: pipeline, tx
func (_m *CiPipelineRepository) Save(pipeline *pipelineConfig.CiPipeline, tx *pg.Tx) error {
	ret := _m.Called(pipeline, tx)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiPipeline, *pg.Tx) error); ok {
		r0 = rf(pipeline, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveCiEnvMapping provides a mock function with given fields: cienvmapping, tx
func (_m *CiPipelineRepository) SaveCiEnvMapping(cienvmapping *pipelineConfig.CiEnvMapping, tx *pg.Tx) error {
	ret := _m.Called(cienvmapping, tx)

	if len(ret) == 0 {
		panic("no return value specified for SaveCiEnvMapping")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiEnvMapping, *pg.Tx) error); ok {
		r0 = rf(cienvmapping, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveCiPipelineScript provides a mock function with given fields: ciPipelineScript, tx
func (_m *CiPipelineRepository) SaveCiPipelineScript(ciPipelineScript *pipelineConfig.CiPipelineScript, tx *pg.Tx) error {
	ret := _m.Called(ciPipelineScript, tx)

	if len(ret) == 0 {
		panic("no return value specified for SaveCiPipelineScript")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiPipelineScript, *pg.Tx) error); ok {
		r0 = rf(ciPipelineScript, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveExternalCi provides a mock function with given fields: pipeline, tx
func (_m *CiPipelineRepository) SaveExternalCi(pipeline *pipelineConfig.ExternalCiPipeline, tx *pg.Tx) (*pipelineConfig.ExternalCiPipeline, error) {
	ret := _m.Called(pipeline, tx)

	if len(ret) == 0 {
		panic("no return value specified for SaveExternalCi")
	}

	var r0 *pipelineConfig.ExternalCiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.ExternalCiPipeline, *pg.Tx) (*pipelineConfig.ExternalCiPipeline, error)); ok {
		return rf(pipeline, tx)
	}
	if rf, ok := ret.Get(0).(func(*pipelineConfig.ExternalCiPipeline, *pg.Tx) *pipelineConfig.ExternalCiPipeline); ok {
		r0 = rf(pipeline, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.ExternalCiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(*pipelineConfig.ExternalCiPipeline, *pg.Tx) error); ok {
		r1 = rf(pipeline, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StartTx provides a mock function with given fields:
func (_m *CiPipelineRepository) StartTx() (*pg.Tx, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for StartTx")
	}

	var r0 *pg.Tx
	var r1 error
	if rf, ok := ret.Get(0).(func() (*pg.Tx, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *pg.Tx); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.Tx)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: pipeline, tx
func (_m *CiPipelineRepository) Update(pipeline *pipelineConfig.CiPipeline, tx *pg.Tx) error {
	ret := _m.Called(pipeline, tx)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiPipeline, *pg.Tx) error); ok {
		r0 = rf(pipeline, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCiEnvMapping provides a mock function with given fields: cienvmapping, tx
func (_m *CiPipelineRepository) UpdateCiEnvMapping(cienvmapping *pipelineConfig.CiEnvMapping, tx *pg.Tx) error {
	ret := _m.Called(cienvmapping, tx)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCiEnvMapping")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiEnvMapping, *pg.Tx) error); ok {
		r0 = rf(cienvmapping, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateCiPipelineScript provides a mock function with given fields: script, tx
func (_m *CiPipelineRepository) UpdateCiPipelineScript(script *pipelineConfig.CiPipelineScript, tx *pg.Tx) error {
	ret := _m.Called(script, tx)

	if len(ret) == 0 {
		panic("no return value specified for UpdateCiPipelineScript")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.CiPipelineScript, *pg.Tx) error); ok {
		r0 = rf(script, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateExternalCi provides a mock function with given fields: pipeline, tx
func (_m *CiPipelineRepository) UpdateExternalCi(pipeline *pipelineConfig.ExternalCiPipeline, tx *pg.Tx) (*pipelineConfig.ExternalCiPipeline, error) {
	ret := _m.Called(pipeline, tx)

	if len(ret) == 0 {
		panic("no return value specified for UpdateExternalCi")
	}

	var r0 *pipelineConfig.ExternalCiPipeline
	var r1 error
	if rf, ok := ret.Get(0).(func(*pipelineConfig.ExternalCiPipeline, *pg.Tx) (*pipelineConfig.ExternalCiPipeline, error)); ok {
		return rf(pipeline, tx)
	}
	if rf, ok := ret.Get(0).(func(*pipelineConfig.ExternalCiPipeline, *pg.Tx) *pipelineConfig.ExternalCiPipeline); ok {
		r0 = rf(pipeline, tx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pipelineConfig.ExternalCiPipeline)
		}
	}

	if rf, ok := ret.Get(1).(func(*pipelineConfig.ExternalCiPipeline, *pg.Tx) error); ok {
		r1 = rf(pipeline, tx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewCiPipelineRepository creates a new instance of CiPipelineRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCiPipelineRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *CiPipelineRepository {
	mock := &CiPipelineRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
