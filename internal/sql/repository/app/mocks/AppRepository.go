// Code generated by mockery v2.20.0. DO NOT EDIT.

package mocks

import (
	app "github.com/devtron-labs/devtron/internal/sql/repository/app"
	helper "github.com/devtron-labs/devtron/internal/sql/repository/helper"

	mock "github.com/stretchr/testify/mock"

	pg "github.com/go-pg/pg"
)

// AppRepository is an autogenerated mock type for the AppRepository type
type AppRepository struct {
	mock.Mock
}

// CheckAppExists provides a mock function with given fields: appNames
func (_m *AppRepository) CheckAppExists(appNames []string) ([]*app.App, error) {
	ret := _m.Called(appNames)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]*app.App, error)); ok {
		return rf(appNames)
	}
	if rf, ok := ret.Get(0).(func([]string) []*app.App); ok {
		r0 = rf(appNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(appNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAllActiveDevtronAppsWithAppIdAndName provides a mock function with given fields:
func (_m *AppRepository) FetchAllActiveDevtronAppsWithAppIdAndName() ([]*app.App, error) {
	ret := _m.Called()

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*app.App, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*app.App); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAllActiveInstalledAppsWithAppIdAndName provides a mock function with given fields:
func (_m *AppRepository) FetchAllActiveInstalledAppsWithAppIdAndName() ([]*app.App, error) {
	ret := _m.Called()

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*app.App, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*app.App); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppIdsWithFilter provides a mock function with given fields: jobListingFilter
func (_m *AppRepository) FetchAppIdsWithFilter(jobListingFilter helper.AppListingFilter) ([]int, error) {
	ret := _m.Called(jobListingFilter)

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func(helper.AppListingFilter) ([]int, error)); ok {
		return rf(jobListingFilter)
	}
	if rf, ok := ret.Get(0).(func(helper.AppListingFilter) []int); ok {
		r0 = rf(jobListingFilter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func(helper.AppListingFilter) error); ok {
		r1 = rf(jobListingFilter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchAppsByFilterV2 provides a mock function with given fields: appNameIncludes, appNameExcludes, environmentId
func (_m *AppRepository) FetchAppsByFilterV2(appNameIncludes string, appNameExcludes string, environmentId int) ([]*app.App, error) {
	ret := _m.Called(appNameIncludes, appNameExcludes, environmentId)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, int) ([]*app.App, error)); ok {
		return rf(appNameIncludes, appNameExcludes, environmentId)
	}
	if rf, ok := ret.Get(0).(func(string, string, int) []*app.App); ok {
		r0 = rf(appNameIncludes, appNameExcludes, environmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, int) error); ok {
		r1 = rf(appNameIncludes, appNameExcludes, environmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveById provides a mock function with given fields: id
func (_m *AppRepository) FindActiveById(id int) (*app.App, error) {
	ret := _m.Called(id)

	var r0 *app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*app.App, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *app.App); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveByName provides a mock function with given fields: appName
func (_m *AppRepository) FindActiveByName(appName string) (*app.App, error) {
	ret := _m.Called(appName)

	var r0 *app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*app.App, error)); ok {
		return rf(appName)
	}
	if rf, ok := ret.Get(0).(func(string) *app.App); ok {
		r0 = rf(appName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(appName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindActiveListByName provides a mock function with given fields: appName
func (_m *AppRepository) FindActiveListByName(appName string) ([]*app.App, error) {
	ret := _m.Called(appName)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]*app.App, error)); ok {
		return rf(appName)
	}
	if rf, ok := ret.Get(0).(func(string) []*app.App); ok {
		r0 = rf(appName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(appName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAll provides a mock function with given fields:
func (_m *AppRepository) FindAll() ([]*app.App, error) {
	ret := _m.Called()

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*app.App, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*app.App); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveAppsWithTeam provides a mock function with given fields:
func (_m *AppRepository) FindAllActiveAppsWithTeam() ([]*app.App, error) {
	ret := _m.Called()

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]*app.App, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []*app.App); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveAppsWithTeamByAppNameMatch provides a mock function with given fields: appNameMatch
func (_m *AppRepository) FindAllActiveAppsWithTeamByAppNameMatch(appNameMatch string) ([]*app.App, error) {
	ret := _m.Called(appNameMatch)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]*app.App, error)); ok {
		return rf(appNameMatch)
	}
	if rf, ok := ret.Get(0).(func(string) []*app.App); ok {
		r0 = rf(appNameMatch)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(appNameMatch)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllActiveAppsWithTeamWithTeamId provides a mock function with given fields: teamID
func (_m *AppRepository) FindAllActiveAppsWithTeamWithTeamId(teamID int) ([]*app.App, error) {
	ret := _m.Called(teamID)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*app.App, error)); ok {
		return rf(teamID)
	}
	if rf, ok := ret.Get(0).(func(int) []*app.App); ok {
		r0 = rf(teamID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(teamID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllMatchesByAppName provides a mock function with given fields: appName, appType
func (_m *AppRepository) FindAllMatchesByAppName(appName string, appType helper.AppType) ([]*app.App, error) {
	ret := _m.Called(appName, appType)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string, helper.AppType) ([]*app.App, error)); ok {
		return rf(appName, appType)
	}
	if rf, ok := ret.Get(0).(func(string, helper.AppType) []*app.App); ok {
		r0 = rf(appName, appType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string, helper.AppType) error); ok {
		r1 = rf(appName, appType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndProjectByAppId provides a mock function with given fields: appId
func (_m *AppRepository) FindAppAndProjectByAppId(appId int) (*app.App, error) {
	ret := _m.Called(appId)

	var r0 *app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*app.App, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) *app.App); ok {
		r0 = rf(appId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndProjectByAppName provides a mock function with given fields: appName
func (_m *AppRepository) FindAppAndProjectByAppName(appName string) (*app.App, error) {
	ret := _m.Called(appName)

	var r0 *app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*app.App, error)); ok {
		return rf(appName)
	}
	if rf, ok := ret.Get(0).(func(string) *app.App); ok {
		r0 = rf(appName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(appName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppAndProjectByIdsIn provides a mock function with given fields: ids
func (_m *AppRepository) FindAppAndProjectByIdsIn(ids []int) ([]*app.App, error) {
	ret := _m.Called(ids)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func([]int) ([]*app.App, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]int) []*app.App); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func([]int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppsByEnvironmentId provides a mock function with given fields: environmentId
func (_m *AppRepository) FindAppsByEnvironmentId(environmentId int) ([]app.App, error) {
	ret := _m.Called(environmentId)

	var r0 []app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]app.App, error)); ok {
		return rf(environmentId)
	}
	if rf, ok := ret.Get(0).(func(int) []app.App); ok {
		r0 = rf(environmentId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(environmentId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppsByTeamId provides a mock function with given fields: teamId
func (_m *AppRepository) FindAppsByTeamId(teamId int) ([]*app.App, error) {
	ret := _m.Called(teamId)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]*app.App, error)); ok {
		return rf(teamId)
	}
	if rf, ok := ret.Get(0).(func(int) []*app.App); ok {
		r0 = rf(teamId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(teamId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppsByTeamIds provides a mock function with given fields: teamId, appType
func (_m *AppRepository) FindAppsByTeamIds(teamId []int, appType string) ([]app.App, error) {
	ret := _m.Called(teamId, appType)

	var r0 []app.App
	var r1 error
	if rf, ok := ret.Get(0).(func([]int, string) ([]app.App, error)); ok {
		return rf(teamId, appType)
	}
	if rf, ok := ret.Get(0).(func([]int, string) []app.App); ok {
		r0 = rf(teamId, appType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]app.App)
		}
	}

	if rf, ok := ret.Get(1).(func([]int, string) error); ok {
		r1 = rf(teamId, appType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAppsByTeamName provides a mock function with given fields: teamName
func (_m *AppRepository) FindAppsByTeamName(teamName string) ([]app.App, error) {
	ret := _m.Called(teamName)

	var r0 []app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]app.App, error)); ok {
		return rf(teamName)
	}
	if rf, ok := ret.Get(0).(func(string) []app.App); ok {
		r0 = rf(teamName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(teamName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindById provides a mock function with given fields: id
func (_m *AppRepository) FindById(id int) (*app.App, error) {
	ret := _m.Called(id)

	var r0 *app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*app.App, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(int) *app.App); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByIds provides a mock function with given fields: ids
func (_m *AppRepository) FindByIds(ids []*int) ([]*app.App, error) {
	ret := _m.Called(ids)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func([]*int) ([]*app.App, error)); ok {
		return rf(ids)
	}
	if rf, ok := ret.Get(0).(func([]*int) []*app.App); ok {
		r0 = rf(ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func([]*int) error); ok {
		r1 = rf(ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindByNames provides a mock function with given fields: appNames
func (_m *AppRepository) FindByNames(appNames []string) ([]*app.App, error) {
	ret := _m.Called(appNames)

	var r0 []*app.App
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]*app.App, error)); ok {
		return rf(appNames)
	}
	if rf, ok := ret.Get(0).(func([]string) []*app.App); ok {
		r0 = rf(appNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(appNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindEnvironmentIdForInstalledApp provides a mock function with given fields: appId
func (_m *AppRepository) FindEnvironmentIdForInstalledApp(appId int) (int, error) {
	ret := _m.Called(appId)

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (int, error)); ok {
		return rf(appId)
	}
	if rf, ok := ret.Get(0).(func(int) int); ok {
		r0 = rf(appId)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(appId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindIdsByNames provides a mock function with given fields: appNames
func (_m *AppRepository) FindIdsByNames(appNames []string) ([]int, error) {
	ret := _m.Called(appNames)

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func([]string) ([]int, error)); ok {
		return rf(appNames)
	}
	if rf, ok := ret.Get(0).(func([]string) []int); ok {
		r0 = rf(appNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func([]string) error); ok {
		r1 = rf(appNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindIdsByTeamIdsAndTeamNames provides a mock function with given fields: teamIds, teamNames
func (_m *AppRepository) FindIdsByTeamIdsAndTeamNames(teamIds []int, teamNames []string) ([]int, error) {
	ret := _m.Called(teamIds, teamNames)

	var r0 []int
	var r1 error
	if rf, ok := ret.Get(0).(func([]int, []string) ([]int, error)); ok {
		return rf(teamIds, teamNames)
	}
	if rf, ok := ret.Get(0).(func([]int, []string) []int); ok {
		r0 = rf(teamIds, teamNames)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int)
		}
	}

	if rf, ok := ret.Get(1).(func([]int, []string) error); ok {
		r1 = rf(teamIds, teamNames)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindJobByDisplayName provides a mock function with given fields: appName
func (_m *AppRepository) FindJobByDisplayName(appName string) (*app.App, error) {
	ret := _m.Called(appName)

	var r0 *app.App
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*app.App, error)); ok {
		return rf(appName)
	}
	if rf, ok := ret.Get(0).(func(string) *app.App); ok {
		r0 = rf(appName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*app.App)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(appName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetConnection provides a mock function with given fields:
func (_m *AppRepository) GetConnection() *pg.DB {
	ret := _m.Called()

	var r0 *pg.DB
	if rf, ok := ret.Get(0).(func() *pg.DB); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pg.DB)
		}
	}

	return r0
}

// Save provides a mock function with given fields: pipelineGroup
func (_m *AppRepository) Save(pipelineGroup *app.App) error {
	ret := _m.Called(pipelineGroup)

	var r0 error
	if rf, ok := ret.Get(0).(func(*app.App) error); ok {
		r0 = rf(pipelineGroup)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SaveWithTxn provides a mock function with given fields: pipelineGroup, tx
func (_m *AppRepository) SaveWithTxn(pipelineGroup *app.App, tx *pg.Tx) error {
	ret := _m.Called(pipelineGroup, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*app.App, *pg.Tx) error); ok {
		r0 = rf(pipelineGroup, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Update provides a mock function with given fields: _a0
func (_m *AppRepository) Update(_a0 *app.App) error {
	ret := _m.Called(_a0)

	var r0 error
	if rf, ok := ret.Get(0).(func(*app.App) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateWithTxn provides a mock function with given fields: _a0, tx
func (_m *AppRepository) UpdateWithTxn(_a0 *app.App, tx *pg.Tx) error {
	ret := _m.Called(_a0, tx)

	var r0 error
	if rf, ok := ret.Get(0).(func(*app.App, *pg.Tx) error); ok {
		r0 = rf(_a0, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewAppRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewAppRepository creates a new instance of AppRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewAppRepository(t mockConstructorTestingTNewAppRepository) *AppRepository {
	mock := &AppRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
