# API Endpoint Fix Guide for Cursor Agents

## Overview
This guide provides a systematic approach to fix API endpoints that are missing the `/orchestrator` prefix, causing 404 errors. Each agent should follow this process for one endpoint at a time.

## Prerequisites
- Access to the devtron codebase
- Terminal access for running commands
- Understanding of the router configuration in `api/router/router.go`

## Root Cause
All API endpoints must be prefixed with `/orchestrator` based on the router configuration. The router sets up subrouters like:
- `/orchestrator/user` for user management
- `/orchestrator/cluster` for cluster management
- `/orchestrator/app` for application management

## Step-by-Step Process

### Step 1: Identify the Target Endpoint
1. **Find incorrect endpoints** in `specs/swagger/openapi.yaml`:
   ```bash
   grep "^  /[^o]" specs/swagger/openapi.yaml
   ```
   This finds all endpoints that don't start with `/orchestrator`

2. **Pick one endpoint** to fix (e.g., `/user/v2`)

### Step 2: Analyze the Current Endpoint
1. **Read the endpoint definition**:
   ```bash
   grep -A 50 "^  /user/v2:" specs/swagger/openapi.yaml
   ```

2. **Check for related endpoints** (same base path):
   ```bash
   grep "^  /user" specs/swagger/openapi.yaml
   ```

### Step 3: Fix the Endpoint
1. **Add `/orchestrator` prefix** using search and replace:
   ```bash
   # For the main endpoint
   sed -i.bak 's|^  /user/v2:|  /orchestrator/user/v2:|' specs/swagger/openapi.yaml
   
   # For related endpoints (if any)
   sed -i.bak 's|^  /user:$|  /orchestrator/user:|' specs/swagger/openapi.yaml
   ```

2. **Verify the change**:
   ```bash
   grep "^  /orchestrator/user" specs/swagger/openapi.yaml
   ```

### Step 4: Test the Fix
1. **Test the incorrect path** (should return 404):
   ```bash
   curl --location 'https://devtron-ent-2.devtron.info/user/v2' \
     --header 'Accept: application/json' \
     --header 'Cookie: argocd.token=YOUR_TOKEN_HERE'
   ```

2. **Test the correct path** (should return 200):
   ```bash
   curl --location 'https://devtron-ent-2.devtron.info/orchestrator/user/v2' \
     --header 'Accept: application/json' \
     --header 'Cookie: argocd.token=YOUR_TOKEN_HERE'
   ```

### Step 5: Document the Fix
1. **Update the log**:
   ```bash
   echo "$(date): Fixed /user/v2 -> /orchestrator/user/v2 ✅" >> api_fix_log.txt
   ```

2. **Clean up backup files**:
   ```bash
   rm -f specs/swagger/openapi.yaml.bak
   ```

## Configuration Files

### 1. Authentication Token
Use this JWT token for testing (valid until 2025):
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQzOTM3MzQsImp0aSI6ImRiODUxYzg5LTg0YjUtNDhiOS1hYTcyLWQ0ZTA0MjRjN2U4MSIsImlhdCI6MTc1NDMwNzMzNCwiaXNzIjoiYXJnb2NkIiwibmJmIjoxNzU0MzA3MzM0LCJzdWIiOiJhZG1pbiJ9.wYGmONdpNUEjtAxXz_mViW44Rxh0YU3dax_SEuoAH5c
```

### 2. Test Server URL
```
https://devtron-ent-2.devtron.info
```

### 3. Common Headers
```bash
--header 'Accept: application/json'
--header 'Cookie: argocd.token=YOUR_TOKEN_HERE'
```

## Endpoint Categories to Fix

### User Management (15 endpoints)
- `/user/v2` → `/orchestrator/user/v2`
- `/user` → `/orchestrator/user`
- `/user/{id}` → `/orchestrator/user/{id}`
- `/user/v2/{id}` → `/orchestrator/user/v2/{id}`
- `/user/bulk` → `/orchestrator/user/bulk`
- `/user/detail/get` → `/orchestrator/user/detail/get`
- `/user/role/group` → `/orchestrator/user/role/group`
- `/user/role/group/{id}` → `/orchestrator/user/role/group/{id}`
- `/user/role/group/detailed/get` → `/orchestrator/user/role/group/detailed/get`
- `/user/role/group/search` → `/orchestrator/user/role/group/search`
- `/user/role/group/bulk` → `/orchestrator/user/role/group/bulk`
- `/user/check/roles` → `/orchestrator/user/check/roles`
- `/user/sync/orchestratortocasbin` → `/orchestrator/user/sync/orchestratortocasbin`
- `/user/update/trigger/terminal` → `/orchestrator/user/update/trigger/terminal`
- `/user/role/cache` → `/orchestrator/user/role/cache`
- `/user/role/cache/invalidate` → `/orchestrator/user/role/cache/invalidate`

### Authentication (6 endpoints)
- `/login` → `/orchestrator/login`
- `/auth/login` → `/orchestrator/auth/login`
- `/auth/callback` → `/orchestrator/auth/callback`
- `/devtron/auth/verify` → `/orchestrator/devtron/auth/verify`
- `/devtron/auth/verify/v2` → `/orchestrator/devtron/auth/verify/v2`
- `/api/v1/session` → `/orchestrator/api/v1/session`

### SSO (5 endpoints)
- `/sso/create` → `/orchestrator/sso/create`
- `/sso/update` → `/orchestrator/sso/update`
- `/sso/list` → `/orchestrator/sso/list`
- `/sso/{id}` → `/orchestrator/sso/{id}`
- `/sso` → `/orchestrator/sso`

### Cluster Management (8 endpoints)
- `/cluster` → `/orchestrator/cluster`
- `/cluster/delete` → `/orchestrator/cluster/delete`
- `/cluster/auth-list` → `/orchestrator/cluster/auth-list`
- `/cluster/validate` → `/orchestrator/cluster/validate`
- `/cluster/saveClusters` → `/orchestrator/cluster/saveClusters`
- `/cluster/{cluster_id}/env` → `/orchestrator/cluster/{cluster_id}/env`

### Environment Management (3 endpoints)
- `/env` → `/orchestrator/env`
- `/env/delete` → `/orchestrator/env/delete`
- `/env/clusters` → `/orchestrator/env/clusters`

### Application Management (6 endpoints)
- `/app/labels/list` → `/orchestrator/app/labels/list`
- `/app/env/patch` → `/orchestrator/app/env/patch`
- `/app/workflow/clone` → `/orchestrator/app/workflow/clone`
- `/app/workflow` → `/orchestrator/app/workflow`
- `/app/workflow/{app-wf-id}/app/{app-id}` → `/orchestrator/app/workflow/{app-wf-id}/app/{app-id}`

### Kubernetes (10 endpoints)
- `/k8s/resource` → `/orchestrator/k8s/resource`
- `/k8s/resource/create` → `/orchestrator/k8s/resource/create`
- `/k8s/resource/delete` → `/orchestrator/k8s/resource/delete`
- `/k8s/events` → `/orchestrator/k8s/events`
- `/k8s/pods/logs/{podName}` → `/orchestrator/k8s/pods/logs/{podName}`
- `/k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}` → `/orchestrator/k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}`
- `/k8s/api-resources/{clusterId}` → `/orchestrator/k8s/api-resources/{clusterId}`
- `/k8s/resource/list` → `/orchestrator/k8s/resource/list`
- `/k8s/resources/rotate` → `/orchestrator/k8s/resources/rotate`
- `/k8s/resources/apply` → `/orchestrator/k8s/resources/apply`

### Batch Operations (7 endpoints)
- `/batch/{apiVersion}/{kind}/readme` → `/orchestrator/batch/{apiVersion}/{kind}/readme`
- `/batch/v1beta1/application/dryrun` → `/orchestrator/batch/v1beta1/application/dryrun`
- `/batch/v1beta1/hibernate` → `/orchestrator/batch/v1beta1/hibernate`
- `/batch/v1beta1/unhibernate` → `/orchestrator/batch/v1beta1/unhibernate`
- `/batch/v1beta1/deploy` → `/orchestrator/batch/v1beta1/deploy`
- `/batch/v1beta1/build` → `/orchestrator/batch/v1beta1/build`
- `/batch/v1beta1/application` → `/orchestrator/batch/v1beta1/application`

### Other (7 endpoints)
- `/resource/history/deployment/cd-pipeline/v1` → `/orchestrator/resource/history/deployment/cd-pipeline/v1`
- `/rbac/roles/default` → `/orchestrator/rbac/roles/default`
- `/refresh` → `/orchestrator/refresh`
- `/admin/policy/default` → `/orchestrator/admin/policy/default`
- `/api/dex/{path}` → `/orchestrator/api/dex/{path}`
- `/version` → `/orchestrator/version`
- `/validate` → `/orchestrator/validate`
- `/config` → `/orchestrator/config`
- `/notification` → `/orchestrator/notification`
- `/notification/recipient` → `/orchestrator/notification/recipient`
- `/notification/channel` → `/orchestrator/notification/channel`

## Testing Templates

### Template 1: Simple GET Request
```bash
curl --location 'https://devtron-ent-2.devtron.info/ENDPOINT_PATH' \
  --header 'Accept: application/json' \
  --header 'Cookie: argocd.token=YOUR_TOKEN_HERE'
```

### Template 2: GET Request with Query Parameters
```bash
curl --location 'https://devtron-ent-2.devtron.info/ENDPOINT_PATH?param1=value1&param2=value2' \
  --header 'Accept: application/json' \
  --header 'Cookie: argocd.token=YOUR_TOKEN_HERE'
```

### Template 3: POST Request with JSON Body
```bash
curl --location 'https://devtron-ent-2.devtron.info/ENDPOINT_PATH' \
  --header 'Accept: application/json' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: argocd.token=YOUR_TOKEN_HERE' \
  --data '{"key": "value"}'
```

## Expected Responses

### Success Response (200 OK)
```json
{
  "code": 200,
  "status": "OK",
  "result": {
    // endpoint-specific data
  }
}
```

### Error Response (404 Not Found)
```html
<html>
<head><title>404 Not Found</title></head>
<body>
<center><h1>404 Not Found</h1></center>
<hr><center>nginx</center>
</body>
</html>
```

## Validation Checklist

For each endpoint fix, verify:

- [ ] **Incorrect path returns 404**: `/endpoint` → 404 Not Found
- [ ] **Correct path returns 200**: `/orchestrator/endpoint` → 200 OK
- [ ] **Response format is correct**: JSON response with proper structure
- [ ] **Authentication works**: Token is accepted
- [ ] **Query parameters work**: If applicable
- [ ] **Related endpoints are fixed**: Check for similar endpoints
- [ ] **Change is documented**: Log the fix

## Common Issues and Solutions

### Issue 1: Endpoint not found in file
**Solution**: Check if the endpoint exists in the file:
```bash
grep "ENDPOINT_NAME" specs/swagger/openapi.yaml
```

### Issue 2: Multiple similar endpoints
**Solution**: Fix all related endpoints:
```bash
grep "^  /base_path" specs/swagger/openapi.yaml
```

### Issue 3: Authentication errors
**Solution**: Verify token is valid and not expired

### Issue 4: Different response format
**Solution**: Check if the endpoint has different expected response format

## Logging Format

Use this format for logging fixes:
```bash
echo "$(date): Fixed OLD_PATH -> NEW_PATH ✅" >> api_fix_log.txt
```

## Completion Criteria

An endpoint is considered fixed when:
1. The incorrect path returns 404
2. The correct path returns 200 OK
3. The response format is valid JSON
4. The fix is documented in the log

## Next Steps After Fixing One Endpoint

1. **Document the fix** in the log
2. **Stop and wait** for user confirmation
3. **Provide summary** of what was fixed
4. **Suggest next endpoint** to fix
5. **Create new agent** for the next endpoint (as requested by user)

## Files to Monitor

- `specs/swagger/openapi.yaml` - Main file with incorrect endpoints
- `api_fix_log.txt` - Log of all fixes
- `API_ENDPOINT_FIX_GUIDE.md` - This guide

## Success Metrics

- [ ] Endpoint returns 200 OK with correct path
- [ ] Endpoint returns 404 with incorrect path
- [ ] Response format is valid
- [ ] Fix is documented
- [ ] No breaking changes to other endpoints 