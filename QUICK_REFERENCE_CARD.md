# Quick Reference Card for API Endpoint Fix

## 🎯 Mission
Fix ONE API endpoint by adding `/orchestrator` prefix to resolve 404 errors.

## 📋 Essential Information

### Test Server & Auth
- **Server**: `https://devtron-ent-2.devtron.info`
- **Token**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQzOTM3MzQsImp0aSI6ImRiODUxYzg5LTg0YjUtNDhiOS1hYTcyLWQ0ZTA0MjRjN2U4MSIsImlhdCI6MTc1NDMwNzMzNCwiaXNzIjoiYXJnb2NkIiwibmJmIjoxNzU0MzA3MzM0LCJzdWIiOiJhZG1pbiJ9.wYGmONdpNUEjtAxXz_mViW44Rxh0YU3dax_SEuoAH5c`

### File to Edit
- **Target**: `specs/swagger/openapi.yaml`

## 🚀 Quick Steps

### 1. Find Next Endpoint
```bash
grep "^  /[^o]" specs/swagger/openapi.yaml | head -1
```

### 2. Fix the Endpoint
```bash
# Replace OLD_PATH with NEW_PATH
sed -i.bak 's|^  OLD_PATH:|  /orchestratorOLD_PATH:|' specs/swagger/openapi.yaml
```

### 3. Test Both Paths
```bash
# Test incorrect path (should be 404)
curl --location 'https://devtron-ent-2.devtron.info/OLD_PATH' \
  --header 'Accept: application/json' \
  --header 'Cookie: argocd.token=TOKEN_HERE'

# Test correct path (should be 200)
curl --location 'https://devtron-ent-2.devtron.info/orchestratorOLD_PATH' \
  --header 'Accept: application/json' \
  --header 'Cookie: argocd.token=TOKEN_HERE'
```

### 4. Log Success
```bash
echo "$(date): Fixed OLD_PATH -> /orchestratorOLD_PATH ✅" >> api_fix_log.txt
```

## ✅ Success Criteria
- ❌ Old path: 404 Not Found
- ✅ New path: 200 OK with JSON response
- 📝 Fix logged in `api_fix_log.txt`

## 🛑 STOP After One Fix
1. Document the fix
2. Provide summary
3. Wait for user to create new agent

## 📚 Full Guide
See `API_ENDPOINT_FIX_GUIDE.md` for complete process and endpoint list. 