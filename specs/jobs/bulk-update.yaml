openapi: "3.0.0"
info:
  version: 1.0.0
  title: Bulk Update - Deployment Template, ConfigMaps, Secrets
servers:
  - url: http://localhost:3000/orchestrator/batch
paths:
  /orchestrator/{apiVersion}/{kind}/readme:
    get:
      description: Returns Readme for bulk update for different resource in the url
      operationId: FindBulkUpdateReadme
      parameters:
        - name: apiVersion
          in: path
          required: true
          schema:
            type: string
        - name: kind
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful GET operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BulkUpdateSeeExampleResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v1beta1/application/dryrun:
    post:
      description: Returns details(id, name, envId) of all apps to be impacted with bulk update
      operationId: GetBulkAppName
      requestBody:
        description: A JSON object containing information by which apps will be filtered
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully return all impacted app details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ImpactedObjectsResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v1beta1/application:
    post:
      description: Bulk Updates all impacted apps
      operationId: BulkUpdate
      requestBody:
        description: A JSON object containing information about update changes and by which apps will be filtered
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully updated all impacted apps.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    BulkUpdateSeeExampleResponse:
      type: object
      required:
        - script
      properties:
        operation:
          type: string
          description: Resource from url path, i.e. {apiVersion} & {kind}
        script:
          $ref: '#/components/schemas/BulkUpdateScript'
        readMe:
          type: string
          description: Readme for bulk update
    BulkUpdateScript:
      type: object
      description: Input Script for bulk edit
      required:
        - apiVersion
        - kind
        - spec
      properties:
        apiVersion:
          type: string
          enum: [ "batch/v1beta1" ]
        kind:
          type: string
          enum: [ "Application" ]
        spec:
          $ref: '#/components/schemas/BulkUpdatePayload'

    BulkUpdatePayload:
      type: object
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
            exclusiveMinimum: 0
          description: Array of Environment Ids of dependent apps
        global:
          type: boolean
<<<<<<<< HEAD:specs/jobs/bulk-update.yaml
          description: Global flag for updating dependent apps
        deploymentTemplate:
          $ref: '#/components/schemas/Tasks'
        configMap:
          $ref: '#/components/schemas/CmAndSecret'
        secret:
          $ref: '#/components/schemas/CmAndSecret'
    Tasks:
      type: object
      properties:
        spec:
          $ref: '#/components/schemas/Spec'
          description: Spec of the Task
    CmAndSecret:
      type: object
      properties:
        spec:
          $ref: '#/components/schemas/CmAndSecretSpec'
          description: Spec of the ConfigMap/Secret
    CmAndSecretSpec:
      type: object
      required:
        - names
      properties:
        names:
          type: array
          items:
            type: string
          description: Names of all configmaps/secrets to be updated
        patchData:
          type: string
          description: string with details of the patch to be used for updating
========
          description: Flag for updating base Configurations of dependent apps
        deploymentTemplate:
          $ref: '#/components/schemas/Spec'
        configMap:
          type: object
          properties:
            allOf:
              $ref: '#/components/schemas/Spec'
            names:
              type: array
              items:
                type: string
                pattern: "^[a-z]+[a-z0-9\\-\\?]*[a-z0-9]+$"
              description: Name of all ConfigMaps to be updated
        secret:
          type: object
          properties:
            allOf:
              $ref: '#/components/schemas/Spec'
            names:
              type: array
              items:
                type: string
              description: Name of all Secrets to be updated

>>>>>>>> develop:specs/bulkEdit/v1beta1/bulk_edit.yaml
    Spec:
      type: object
      properties:
        patchData:
          type: string
<<<<<<<< HEAD:specs/jobs/bulk-update.yaml
          description: string with details of the patch to be used for updating
========
          description: String with details of the patch to be used for updating

>>>>>>>> develop:specs/bulkEdit/v1beta1/bulk_edit.yaml
    NameIncludesExcludes:
      type: object
      properties:
        names:
          type: array
          items:
            type: string
            pattern: "^[a-z%]+[a-z0-9%\\-\\?]*[a-z0-9%]+$"
          description: Array of application names to be included
    ImpactedObjectsResponse:
      type: object
      properties:
        deploymentTemplate:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateImpactedObjectsResponseForOneApp'
        configMap:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
        secret:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
    DeploymentTemplateImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
    CmAndSecretImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
        names:
          type: array
          items:
            type: string
            description: Names of all configmaps/secrets impacted
    BulkUpdateResponse:
      type: object
      properties:
        deploymentTemplate:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponseForOneApp'
        configMap:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
        secret:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
    DeploymentTemplateBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
        message:
          type: string
          description: Message indicating success or failure of the update
    CmAndSecretBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
        names:
          type: array
          items:
            type: string
          description: Names of all configmaps/secrets impacted
        message:
          type: string
          description: Message indicating success or failure of the update
    Error:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code
        message:
          type: string
          description: Error message