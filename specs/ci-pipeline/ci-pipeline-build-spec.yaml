openapi: 3.0.0
info:
  version: 1.0.0
  title: CI Pipeline Build API
  description: API for managing CI pipeline builds and related operations
  contact:
    name: Devtron Support
    url: https://devtron.ai
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html

servers:
  - url: /orchestrator
    description: Devtron Orchestrator API Server

paths:
  /orchestrator/commit-info/{ciPipelineMaterialId}/{gitHash}:
    get:
      tags:
        - Commit
      summary: Get commit information
      description: Retrieves commit information for a specific commit hash in a CI pipeline material
      operationId: getCommitInfo
      parameters:
        - name: ciPipelineMaterialId
          in: path
          description: ID of the CI pipeline material
          required: true
          schema:
            type: integer
            format: int32
            minimum: 1
          example: 123
        - name: gitHash
          in: path
          description: Git commit hash
          required: true
          schema:
            type: string
            pattern: '^[a-f0-9]{7,40}$'
          example: "a1b2c3d4e5"
      responses:
        '200':
          description: Successfully retrieved commit information
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommitInfo'
        '400':
          description: Invalid input parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '404':
          description: Commit not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
      security:
        - bearerAuth: []
          
  /ci-pipeline/trigger:
    post:
      tags:
        - Workflow
      summary: Trigger CI workflow
      description: Triggers a new CI workflow for the specified pipeline
      operationId: triggerWorkflow
      requestBody:
        description: Workflow trigger payload
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkflowTriggerRequest'
      responses:
        '200':
          description: Workflow triggered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
      security:
        - bearerAuth: []

  /{appId}/ci-pipeline/{pipelineId}/workflow/{workflowId}:
    get:
      tags:
        - Workflow
      summary: Get workflow details
      description: Retrieves details of a specific workflow
      operationId: getWorkflow
      parameters:
        - name: appId
          in: path
          description: ID of the application
          required: true
          schema:
            type: integer
            format: int32
            minimum: 1
          example: 123
        - name: pipelineId
          in: path
          description: ID of the CI pipeline
          required: true
          schema:
            type: integer
            format: int32
            minimum: 1
          example: 456
        - name: workflowId
          in: path
          description: ID of the workflow
          required: true
          schema:
            type: integer
            format: int32
            minimum: 1
          example: 789
      responses:
        '200':
          description: Workflow details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkflowResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '404':
          description: Workflow not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'
      security:
        - bearerAuth: []

components:
  schemas:
    CommitInfo:
      type: object
      required:
        - Commit
        - Author
        - Date
      properties:
        Commit:
          type: string
          description: Full commit hash
          example: "a1b2c3d4e5f6g7h8i9j0"
          minLength: 7
          maxLength: 40
        Author:
          type: string
          description: Author of the commit
          example: "John Doe <<EMAIL>>"
        Date:
          type: string
          format: date-time
          description: Timestamp of the commit
          example: "2023-01-15T14:30:22Z"
        Message:
          type: string
          description: Commit message
          example: "Update README with new features"
        Changes:
          type: array
          description: List of files changed in this commit
          items:
            type: string
            example: "src/main.go"

    WorkflowTriggerRequest:
      type: object
      required:
        - pipelineId
        - ciPipelineMaterials
      properties:
        pipelineId:
          type: integer
          description: ID of the CI pipeline to trigger
          example: 123
        ciPipelineMaterials:
          type: array
          description: CI pipeline materials configuration
          items:
            $ref: '#/components/schemas/CiPipelineMaterial'
        triggeredBy:
          type: integer
          description: User ID who triggered the pipeline
          example: 1
        invalidateCache:
          type: boolean
          description: Whether to invalidate cache
          default: false
        environmentId:
          type: integer
          description: Environment ID for the pipeline
          example: 456
        pipelineType:
          type: string
          description: Type of pipeline
          example: "CI"
        ciArtifactLastFetch:
          type: string
          format: date-time
          description: Last fetch time for CI artifacts

    CiPipelineMaterial:
      type: object
      properties:
        Id:
          type: integer
          description: Material ID
          example: 1
        GitMaterialId:
          type: integer
          description: Git material ID
          example: 2
        Type:
          type: string
          description: Type of material
          example: "GIT"
        Value:
          type: string
          description: Material value
          example: "main"
        Active:
          type: boolean
          description: Whether material is active
          default: true
        GitCommit:
          $ref: '#/components/schemas/GitCommit'
        GitTag:
          type: string
          description: Git tag
          example: "v1.0.0"

    GitCommit:
      type: object
      properties:
        Commit:
          type: string
          description: Commit hash
          example: "a1b2c3d4e5"
        Author:
          type: string
          description: Author name
          example: "John Doe"
        Date:
          type: string
          format: date-time
          description: Commit date
          example: "2023-01-15T14:30:22Z"
        Message:
          type: string
          description: Commit message
          example: "Update README"

    CIBuildConfig:
      type: object
      properties:
        dockerBuildConfig:
          $ref: '#/components/schemas/DockerBuildConfig'
        ciBuildType:
          type: string
          enum: [SELF_DOCKERFILE_BUILD_TYPE, MANIFEST_PUSH, SKIP_BUILD]

    DockerBuildConfig:
      type: object
      properties:
        dockerfilePath:
          type: string
          example: "./Dockerfile"
        args:
          type: object
          additionalProperties:
            type: string

    WorkflowResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 12345
        name:
          type: string
          example: "workflow-12345"
        status:
          type: string
          enum: [QUEUED, RUNNING, SUCCEEDED, FAILED, ABORTED]
          example: "QUEUED"
        startTime:
          type: string
          format: date-time
          example: "2023-01-15T14:30:22Z"
        endTime:
          type: string
          format: date-time
          example: "2023-01-15T14:35:45Z"
        triggeredBy:
          type: string
          example: "<EMAIL>"

    ApiError:
      type: object
      properties:
        code:
          type: integer
          format: int32
          example: 404
        message:
          type: string
          example: "Resource not found"
        details:
          type: array
          items:
            type: string
          example: ["Commit not found in the repository"]

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: >-
        JWT token for authentication. 
        Include the token in the Authorization header as: 'Bearer {token}'