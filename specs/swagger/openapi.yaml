openapi: 3.0.0
info:
  version: 1.0.0
  title: Devtron APIs Specs
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
- url: http://localhost/orchestrator
  description: Local development server
tags:
- name: Metadata
  description: Application metadata and information retrieval
  x-displayName: Application Metadata
- name: Jobs
  description: Job management operations for creating, cloning, and retrieving jobs
  x-displayName: Job Management
- name: Helm Charts
  description: Helm chart deployment management and operations
  x-displayName: Helm Chart Management
- name: List Applications
  x-displayName: List Applications
  description: Application listing
- name: Applications
  description: Application management operations including creation, listing, and
    updates
  x-displayName: Application Management
- name: Labels
  x-displayName: Labels
- name: bulk_other
  x-displayName: BulkGetData
- name: BulkUpdate
  x-displayName: BulkUpdate
- name: SSO Configuration
  description: Manage Single Sign-On (SSO) provider configurations.
  x-displayName: SSO Configuration
- name: User Management
  description: Operations related to user accounts (CRUD, listing, bulk actions).
  x-displayName: User Management
- name: Role Group Management
  description: Operations related to user role groups (CRUD, listing, bulk actions).
  x-displayName: Role Group Management
- name: RBAC
  description: Operations related to Role-Based Access Control, like fetching default
    roles.
  x-displayName: RBAC
- name: Authentication
  description: Core authentication endpoints including login, token refresh, and auth
    verification.
  x-displayName: Authentication
- name: Policy Management
  description: Endpoints for managing policies.
  x-displayName: Policy Management
- name: Cache Management
  description: Endpoints for managing authentication and authorization caches.
  x-displayName: Cache Management
- name: Cluster Environment
  description: Operations related to clusters and environments
  x-displayName: Cluster Environment
- name: Cluster Management
  description: Operations related to cluster creation, update, and validation
  x-displayName: Cluster Management
- name: Environment Management
  description: Operations for creating, updating, and deleting environments
  x-displayName: Environment Management
- name: Change Chart
  x-displayName: ChangeChartType
- name: Clone Workflow
  x-displayName: CloneWorkflow (ENT)
- name: Deployment History
  x-displayName: Get Deployment History (ENT)
  description: Retrieves the deployment history for a specific CD pipeline based on
    various filter criteria.
- name: K8s Resource
  description: APIs for managing Kubernetes resources (get, create, update, delete,
    list).
  x-displayName: K8s Resource
- name: Workflow Management
  x-displayName: Workflow Management
- name: Notifications
  x-displayName: Notifiactions
- name: Devtron Server version
  x-displayName: Devtron Server version
- name: GitOps Validation
  x-displayName: GitOps Validation
paths:
  /orchestrator/resource/history/deployment/cd-pipeline/v1:
    get:
      summary: Get Deployment History
      description: Retrieves the deployment history for a specific CD pipeline based
        on various filter criteria.
      operationId: getDeploymentHistory
      security: []
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: filterCriteria
        in: query
        required: true
        schema:
          type: array
          items:
            type: string
        description: 'Filter criteria for deployment history. Example:

          - application/devtron-application|id|1064

          - environment|id|146

          - release|id|203

          '
      - name: offset
        in: query
        required: false
        schema:
          type: integer
        description: The starting point for fetching records (pagination).
      - name: limit
        in: query
        required: false
        schema:
          type: integer
        description: The number of records to return (pagination).
      responses:
        '200':
          description: Successful retrieval of deployment history.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: OK
                  result:
                    type: object
                    properties:
                      cdWorkflows:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 19767
                            cd_workflow_id:
                              type: integer
                              example: 19576
                            name:
                              type: string
                              example: cd-1064-nu4s
                            status:
                              type: string
                              example: Succeeded
                            pod_status:
                              type: string
                              example: ''
                            message:
                              type: string
                              example: ''
                            started_on:
                              type: string
                              format: date-time
                              example: '2024-07-25T08:36:42.414376Z'
                            finished_on:
                              type: string
                              format: date-time
                              example: '2024-07-25T08:40:03.455303Z'
                            pipeline_id:
                              type: integer
                              example: 2082
                            namespace:
                              type: string
                              example: devtron-cd
                            log_file_path:
                              type: string
                              example: ''
                            triggered_by:
                              type: integer
                              example: 23
                            email_id:
                              type: string
                              example: <EMAIL>
                            image:
                              type: string
                              example: devtroninc.azurecr.io/test:8a0c2298-fc94fa4b-956-18655
                            ci_artifact_id:
                              type: integer
                              example: 14593
                            workflow_type:
                              type: string
                              example: DEPLOY
                            blobStorageEnabled:
                              type: boolean
                              example: true
                            userApprovalMetadata:
                              type: object
                              nullable: true
                            gitTriggers:
                              type: object
                              additionalProperties:
                                type: object
                                properties:
                                  Commit:
                                    type: string
                                    example: 8a0c22983ae7acae10abe7569026ae25d889e159
                                  Author:
                                    type: string
                                    example: jatin-jangir-0220 <<EMAIL>>
                                  Date:
                                    type: string
                                    format: date-time
                                    example: '2024-04-29T18:49:39Z'
                                  Message:
                                    type: string
                                    example: 'Update Dockerfile (#19)


                                      '
                                  Changes:
                                    type: string
                                    nullable: true
                                  WebhookData:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                        example: 0
                                      eventActionType:
                                        type: string
                                        example: ''
                                      data:
                                        type: string
                                        nullable: true
                                  CiConfigureSourceValue:
                                    type: string
                                    example: main
                                  GitRepoUrl:
                                    type: string
                                    example: https://github.com/devtron-labs/casbin-enterprise
                                  GitRepoName:
                                    type: string
                                    example: casbin-enterprise
                                  CiConfigureSourceType:
                                    type: string
                                    example: SOURCE_TYPE_BRANCH_FIXED
                            ciMaterials:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 1013
                                  gitMaterialId:
                                    type: integer
                                    example: 411
                                  gitMaterialUrl:
                                    type: string
                                    example: ''
                                  gitMaterialName:
                                    type: string
                                    example: casbin-enterprise
                                  type:
                                    type: string
                                    example: SOURCE_TYPE_BRANCH_FIXED
                                  value:
                                    type: string
                                    example: main
                                  active:
                                    type: boolean
                                    example: true
                                  lastFetchTime:
                                    type: string
                                    format: date-time
                                    example: '0001-01-01T00:00:00Z'
                                  isRepoError:
                                    type: boolean
                                    example: false
                                  repoErrorMsg:
                                    type: string
                                    example: ''
                                  isBranchError:
                                    type: boolean
                                    example: false
                                  branchErrorMsg:
                                    type: string
                                    example: ''
                                  url:
                                    type: string
                                    example: https://github.com/devtron-labs/casbin-enterprise
                                  regex:
                                    type: string
                                    example: ''
                      tagsEditable:
                        type: boolean
                        example: false
                      appReleaseTagNames:
                        type: array
                        items:
                          type: string
                        example: []
                      hideImageTaggingHardDelete:
                        type: boolean
                        example: false
              examples:
                example-1:
                  value:
                    code: 200
                    status: OK
                    result:
                      cdWorkflows:
                      - id: 19752
                        cd_workflow_id: 19561
                        name: cd-1064-nu4s
                        status: Failed
                        pod_status: ''
                        message: 'Unable to continue with install: could not get information
                          about the resource Job "" in namespace "devtroncd": resource
                          name may not be empty'
                        started_on: '2024-07-25T08:26:21.792068Z'
                        finished_on: '2024-07-25T08:26:23.855384Z'
                        pipeline_id: 2082
                        namespace: devtron-cd
                        log_file_path: ''
                        triggered_by: 23
                        email_id: <EMAIL>
                        image: devtroninc.azurecr.io/test:8a0c2298-fc94fa4b-956-18655
                        ci_artifact_id: 14593
                        workflow_type: DEPLOY
                        blobStorageEnabled: true
                        userApprovalMetadata: null
                        gitTriggers:
                          '1013':
                            Commit: 8a0c22983ae7acae10abe7569026ae25d889e159
                            Author: jatin-jangir-0220 <<EMAIL>>
                            Date: '2024-04-29T18:49:39Z'
                            Message: 'Update Dockerfile (#19)


                              '
                            Changes: null
                            WebhookData:
                              id: 0
                              eventActionType: ''
                              data: null
                            CiConfigureSourceValue: main
                            GitRepoUrl: https://github.com/devtron-labs/casbin-enterprise
                            GitRepoName: casbin-enterprise
                            CiConfigureSourceType: SOURCE_TYPE_BRANCH_FIXED
                          '2072':
                            Commit: fc94fa4bad21460e822ce896b5166273aa3df1a8
                            Author: Gireesh Naidu <<EMAIL>>
                            Date: '2024-07-23T18:13:18+05:30'
                            Message: "fix: getting 500 while updating the branch of\
                              \ linked CI (#1424)\n\n* fix: sync ci pipeline materials\
                              \ for linked pipelines\r\n\r\n* fix: sync ci pipeline\
                              \ materials for linked pipelines\r\n\r\n* fix: null\
                              \ column fix\r\n\r\n* fix: null column fix\r\n\r\n*\
                              \ fix: able to delete ci pipeline though it has linked\
                              \ ci's using API"
                            Changes: null
                            WebhookData:
                              id: 0
                              eventActionType: ''
                              data: null
                            CiConfigureSourceValue: main
                            GitRepoUrl: https://github.com/devtron-labs/devtron-enterprise
                            GitRepoName: devtron-enterprise
                            CiConfigureSourceType: SOURCE_TYPE_BRANCH_FIXED
                        ciMaterials:
                        - id: 1013
                          gitMaterialId: 411
                          gitMaterialUrl: ''
                          gitMaterialName: casbin-enterprise
                          type: SOURCE_TYPE_BRANCH_FIXED
                          value: main
                          active: true
                          lastFetchTime: '0001-01-01T00:00:00Z'
                          isRepoError: false
                          repoErrorMsg: ''
                          isBranchError: false
                          branchErrorMsg: ''
                          url: https://github.com/devtron-labs/casbin-enterprise
                          regex: ''
                        - id: 2072
                          gitMaterialId: 1286
                          gitMaterialUrl: ''
                          gitMaterialName: devtron-enterprise
                          type: SOURCE_TYPE_BRANCH_FIXED
                          value: main
                          active: true
                          lastFetchTime: '0001-01-01T00:00:00Z'
                          isRepoError: false
                          repoErrorMsg: ''
                          isBranchError: false
                          branchErrorMsg: ''
                          url: https://github.com/devtron-labs/devtron-enterprise
                          regex: ''
                        imageReleaseTags: null
                        imageComment: null
                        referenceCdWorkflowRunnerId: 0
                        appliedFilters: null
                        appliedFiltersState: 0
                        appliedFiltersTimestamp: '0001-01-01T00:00:00Z'
                        promotionApprovalMetadata: null
                        runSource:
                          kind: release
                          version: alpha1
                          id: 203
                          identifier: qa-releases-track-0.0.2
                          releaseVersion: 0.0.2
                          name: deepak-qa-release
                          releaseTrackName: qa-releases-track
                        targetConfig:
                          tenantId: qa-devtroncd-x
                          tenantName: QA Devtron Env
                          installationId: qa-devtroncd-2
                          installationName: qa-devtroncd-2
                          releaseChannelId: beta
                          releaseChannelName: beta-channel
      tags:
      - Deployment History
  /orchestrator/app/labels/list:
    get:
      summary: List all app labels
      description: This API will return all the labels available in the database.
      operationId: listAppLabels
      security: []
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    allOf:
                    - type: object
                      properties:
                        appId:
                          type: integer
                          description: unique application id
                      required:
                      - appId
                    - $ref: '#/components/schemas/AppLabel'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '404':
          description: Not found
        '500':
          description: Internal server error
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Metadata
  /orchestrator/app/meta/info/{appId}:
    get:
      summary: Get application meta info
      description: Application basic info, projects and labels
      operationId: getAppMetaInfo
      security: []
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: appId
        in: path
        description: application id
        required: true
        schema:
          type: integer
          format: int64
      responses:
        '200':
          description: application basic info, projects and labels
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: meta info project name and labels
                    $ref: '#/components/schemas/AppMetaInfo'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '404':
          description: Not found
        '500':
          description: Internal server error
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Metadata
  /orchestrator/helm/meta/info/{appId}:
    get:
      summary: Get Helm application meta info
      description: Application info for all types of Helm apps
      operationId: getHelmAppMetaInfo
      security: []
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: appId
        in: path
        description: application id
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Helm application basic info
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: meta info project name and labels
                    $ref: '#/components/schemas/AppMetaInfo'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '404':
          description: Not found
        '500':
          description: Internal server error
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Metadata
  /orchestrator/job:
    post:
      summary: Create or clone a job
      description: Create and clone a job
      operationId: createOrCloneJob
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateJob'
      responses:
        '200':
          description: Used to give response once a job is created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActionResponse'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Internal server error
      tags:
      - Jobs
  /orchestrator/job/list:
    post:
      summary: List jobs
      description: Get the list of all the jobs by applying filter
      operationId: listJobs
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/JobList'
      responses:
        '200':
          description: Used to give response of list of jobs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/JobListResponse'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Internal server error
      tags:
      - Jobs
  /orchestrator/job/ci-pipeline/list/{jobId}:
    get:
      summary: Get job CI pipeline list
      description: fetch details of job ci-pipelines for the overview page
      operationId: getJobCiPipelineList
      security: []
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: jobId
        in: path
        required: true
        description: Job Id
        schema:
          type: integer
      responses:
        '200':
          description: Job CI pipeline list
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Internal server error
      tags:
      - Jobs
  /orchestrator/app-store/installed-app:
    get:
      summary: List deployed charts
      operationId: listDeployedCharts
      description: deployed chart listing, with search filters
      security: []
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: envs
        in: query
        description: environment ids
        required: false
        schema:
          type: array
          items:
            type: string
      - name: chartRepoId
        in: query
        description: chart repo ids
        required: false
        schema:
          type: array
          items:
            type: string
      - name: appStoreName
        in: query
        description: chart name
        required: false
        schema:
          type: string
      - name: appName
        in: query
        description: chart name as app name for devtron
        required: false
        schema:
          type: string
      - name: onlyDeprecated
        in: query
        description: show only deprecated or all
        required: false
        schema:
          type: boolean
      - name: offset
        in: query
        description: offset for result set
        required: false
        schema:
          type: integer
      - name: size
        in: query
        description: total request size.
        required: false
        schema:
          type: integer
      responses:
        '200':
          description: deployed chart listing, with search filters
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: deployed chart listing, with search filters
                    items:
                      $ref: '#/components/schemas/ChartInfo'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Helm Charts
  /orchestrator/app-store/installed-app/notes:
    get:
      summary: Fetch notes.txt for deployed helm charts
      operationId: fetchNotesTxt
      description: Used to fetch notes.txt for helm charts deployed via gitOps
      security: []
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: env-id
        in: query
        description: it is an environment id of app
        required: true
        schema:
          type: integer
      - name: installed-app-id
        in: query
        description: it is a installed application id
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: if it is able to fetch the notes.txt then status will be ok
          content:
            application/json:
              schema:
                properties:
                  notes:
                    type: string
                    description: it will provide notes
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: error while fetching notes.txt
      tags:
      - Helm Charts
  /orchestrator/app/autocomplete:
    get:
      summary: List application autocomplete
      operationId: listAppAutocomplete
      description: list of namespaces group by clusters
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - in: query
        name: appName
        example: abc
        description: app name, wildcard query
        required: false
        allowEmptyValue: true
        schema:
          type: string
      - in: query
        name: teamId
        example: '1'
        description: project id
        required: false
        allowEmptyValue: false
        schema:
          type: integer
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: app list
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - List Applications
      security: []
  /orchestrator/app:
    post:
      summary: Create a new application
      operationId: createApplication
      description: create new application
      security: []
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/App'
      responses:
        '200':
          description: App create response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: string
                    allOf:
                    - type: object
                      properties:
                        id:
                          type: integer
                          description: unique application id
                      required:
                      - id
                    - $ref: '#/components/schemas/App'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Applications
  /orchestrator/app/edit:
    post:
      summary: Update application projects and labels
      operationId: updateApplicationProjectsAndLabels
      description: update application projects and labels
      security: []
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/App'
      responses:
        '200':
          description: App update response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: string
                    allOf:
                    - type: object
                      properties:
                        id:
                          type: integer
                          description: unique application id
                      required:
                      - id
                    - $ref: '#/components/schemas/App'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Applications
  /orchestrator/app/list:
    post:
      summary: List applications
      operationId: listApplications
      description: app listing, collection of deployed applications or undeployed
        or incomplete configured apps.
      security: []
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppListingRequest'
      responses:
        '200':
          description: App create response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: string
                    required:
                    - appCount
                    - appContainers
                    - deploymentGroup
                    properties:
                      appCount:
                        type: integer
                        description: app count, total number of apps available based
                          on filter provided in request.
                      appContainers:
                        type: array
                        description: app containers
                        items:
                          $ref: '#/components/schemas/AppContainer'
                      deploymentGroup:
                        type: object
                        description: deployment group
                        $ref: '#/components/schemas/DeploymentGroup'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Applications
  /orchestrator/app/edit/projects:
    post:
      summary: Update project for app
      operationId: updateProjectForApp
      description: update project for app
      security: []
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppProjectUpdateRequest'
      responses:
        '200':
          description: App update response
          content:
            application/json:
              schema:
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: object
                    description: string
                    $ref: '#/components/schemas/AppProjectUpdateRequest'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      tags:
      - Applications
  /orchestrator/app/labels/list:
    get:
      summary: List Application Labels
      description: "Retrieves a list of application labels. By default, returns all\
        \ labels. \nUse the `showPropagatedOnly` parameter to filter for labels where\
        \ propagate = true.\n\n**Required Token Permission:**\n- Must have **View**\
        \ access to the applications in scope.\n"
      operationId: getAppLabels
      tags:
      - Labels
      security: []
      
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: showPropagatedOnly
        in: query
        description: "If true, only returns labels where propagate = true. \nIf false\
          \ or not provided, all labels are returned.\n"
        required: false
        schema:
          type: boolean
          default: false
        example: false
      responses:
        '200':
          description: Successfully retrieved labels list
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: HTTP status code
                    example: 200
                  status:
                    type: string
                    description: Response status message
                    example: OK
                  result:
                    type: array
                    description: Array of label objects
                    items:
                      type: object
                      required:
                      - key
                      - value
                      - propagate
                      - appId
                      - appName
                      properties:
                        key:
                          type: string
                          description: The label key/name
                          example: environment
                        value:
                          type: string
                          description: The label value
                          example: production
                        propagate:
                          type: boolean
                          description: Whether this label should be propagated
                          example: true
                        appId:
                          type: integer
                          description: The unique identifier of the application
                          example: 1234
                        appName:
                          type: string
                          description: The name of the application
                          example: web-service
              examples:
                all_labels:
                  summary: All labels response
                  description: Example response when showPropagatedOnly is false or
                    not provided
                  value:
                    code: 200
                    status: OK
                    result:
                    - key: environment
                      value: production
                      propagate: true
                      appId: 1234
                      appName: web-service
                    - key: team
                      value: backend
                      propagate: false
                      appId: 1234
                      appName: web-service
                propagated_only:
                  summary: Propagated labels only
                  description: Example response when showPropagatedOnly is true
                  value:
                    code: 200
                    status: OK
                    result:
                    - key: environment
                      value: production
                      propagate: true
                      appId: 1234
                      appName: web-service
        '401':
          description: Authentication required or token invalid
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 401
                  status:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Authentication token is required
        '403':
          description: Insufficient permissions to access the resource
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 403
                  status:
                    type: string
                    example: Forbidden
                  message:
                    type: string
                    example: Token does not have View access to the applications in
                      scope
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 500
                  status:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: An unexpected error occurred
  /orchestrator/batch/{apiVersion}/{kind}/readme:
    get:
      summary: Get Readme for Bulk Update
      description: Returns Readme for bulk update for different resource in the url
      operationId: FindBulkUpdateReadme
      security: [] 
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: apiVersion
        in: path
        required: true
        description: apiVersion of the resource
        schema:
          type: string
      - name: kind
        in: path
        required: true
        description: kind of the resource
        schema:
          type: string
      responses:
        '200':
          description: Successful GET operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateSeeExampleResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      tags:
      - bulk_other
  /orchestrator/batch/v1beta1/application/dryrun:
    post:
      summary: Dry Run for Bulk Application Update
      description: Returns details(id, name, envId) of all apps to be impacted with
        bulk update
      operationId: GetBulkAppName
      security: []
      
      requestBody:
        description: A JSON object containing information by which apps will be filtered
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully return all impacted app details.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ImpactedObjectsResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      tags:
      - bulk_other
  /orchestrator/batch/v1beta1/hibernate:
    post:
      summary: Bulk Hibernate Applications
      description: Bulk Hibernates applications
      operationId: BulkHibernate
      security: []
      
      tags:
      - BulkUpdate
      requestBody:
        description: A JSON object containing information about applications and environments
          to hibernate.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkActionRequest'
      responses:
        '200':
          description: Successfully hibernated applications.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/batch/v1beta1/unhibernate:
    post:
      summary: Bulk Un-Hibernate Applications
      description: Bulk Un-Hibernates applications
      operationId: BulkUnHibernate
      security: []
      
      tags:
      - BulkUpdate
      requestBody:
        description: A JSON object containing information about applications and environments
          to un-hibernate.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkActionRequest'
      responses:
        '200':
          description: Successfully un-hibernated applications.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/batch/v1beta1/deploy:
    post:
      summary: Bulk Deploy Applications
      description: Bulk Triggers deployment of applications
      operationId: BulkDeploy
      security: []
      
      tags:
      - BulkUpdate
      requestBody:
        description: A JSON object containing information for bulk deployment.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkDeployRequest'
      responses:
        '200':
          description: Successfully triggered bulk deployment.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/batch/v1beta1/build:
    post:
      summary: Bulk Trigger Application Builds
      description: Bulk Triggers build of applications
      operationId: BulkBuildTrigger
      security: []
      
      tags:
      - BulkUpdate
      requestBody:
        description: A JSON object containing information for bulk build trigger.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkBuildTriggerRequest'
      responses:
        '200':
          description: Successfully triggered bulk build.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkActionResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/batch/v1beta1/application:
    post:
      summary: Bulk Edit Applications
      description: Bulk Updates (Edit) all impacted apps. This endpoint can be used
        for bulk editing application configurations like deployment templates, configmaps,
        and secrets.
      operationId: BulkUpdate
      security: []
      
      tags:
      - BulkUpdate
      requestBody:
        description: A JSON object containing information about update changes and
          by which apps will be filtered for bulk editing.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUpdateScript'
      responses:
        '200':
          description: Successfully updated all impacted apps.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUpdateResponse'
        '400':
          description: Bad Request. Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/sso/create:
    post:
      tags:
      - SSO Configuration
      summary: Create SSO Login Configuration
      operationId: CreateSSOLoginConfig
      requestBody:
        $ref: '#/components/requestBodies/SSOLoginDto'
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/sso/update:
    put:
      tags:
      - SSO Configuration
      summary: Update SSO Login Configuration
      operationId: UpdateSSOLoginConfig
      requestBody:
        $ref: '#/components/requestBodies/SSOLoginDto'
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/sso/list:
    get:
      tags:
      - SSO Configuration
      summary: Get All SSO Login Configurations
      operationId: GetAllSSOLoginConfig
      responses:
        '200':
          description: List of SSO configurations.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SSOLoginDto'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/sso/{id}:
    get:
      tags:
      - SSO Configuration
      summary: Get SSO Login Configuration by ID
      operationId: GetSSOLoginConfig
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/sso:
    get:
      tags:
      - SSO Configuration
      summary: Get SSO Login Configuration by Name
      operationId: GetSSOLoginConfigByName
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: name
        in: query
        required: true
        description: Name of the SSO configuration.
        schema:
          type: string
      responses:
        '200':
          $ref: '#/components/responses/SSOLoginConfigResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/rbac/roles/default:
    get:
      tags:
      - RBAC
      summary: Get All Default Roles
      operationId: GetAllDefaultRoles
      responses:
        '200':
          description: List of default RBAC roles.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RbacRoleDto'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/api/v1/session:
    post:
      tags:
      - Authentication
      summary: User Login
      description: Authenticates a user and returns a session token.
      operationId: LoginHandler
      requestBody:
        description: Username and password for login.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                  description: Username
                  example: admin
                password:
                  type: string
                  description: Password
                  format: password
                  example: password123
              required:
              - username
              - password
      responses:
        '200':
          description: Login successful, token returned.
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
          headers:
            Set-Cookie:
              description: Sets the argocd.token cookie.
              schema:
                type: string
                example: argocd.token=yourtokenvalue; Path=/
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          description: Invalid username or password.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: []
      
  /orchestrator/refresh:
    get:
      tags:
      - Authentication
      summary: Refresh Session Token
      operationId: RefreshTokenHandler
      description: Handles token refresh (details depend on OIDC/OAuth flow).
      responses:
        '200':
          description: Token refreshed successfully.
        '401':
          $ref: '#/components/responses/Unauthorized'
      security: []
      
  /orchestrator/admin/policy/default:
    post:
      tags:
      - Policy Management
      summary: Add Default Policy and Roles
      operationId: AddDefaultPolicyAndRoles
      description: Creates default policies and roles based on team, app, and environment.
        This is a specialized endpoint.
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: team
        in: query
        required: true
        description: Project Id 
        schema:
          type: string
      - name: app
        in: query
        required: true
        description: Application Id
        schema:
          type: string
      - name: env
        in: query
        required: true
        description: Environment Id
        schema:
          type: string
      responses:
        '200':
          description: Default policies and roles added successfully.
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/devtron/auth/verify:
    get:
      tags:
      - Authentication
      summary: Verify Authentication Status
      operationId: AuthVerification
      responses:
        '200':
          description: Authentication status.
          content:
            application/json:
              schema:
                type: boolean
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/devtron/auth/verify/v2:
    get:
      tags:
      - Authentication
      summary: Verify Authentication Status (V2)
      operationId: AuthVerificationV2
      responses:
        '200':
          description: Detailed authentication status including super admin flag.
          content:
            application/json:
              schema:
                type: object
                properties:
                  isSuperAdmin:
                    type: boolean
                  isVerified:
                    type: boolean
                  emailId:
                    type: string
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/v2:
    get:
      tags:
      - User Management
      summary: List Users (V2 - Paginated, Filtered)
      operationId: GetAllUsersV2
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/SearchKeyQuery'
      - $ref: '#/components/parameters/SortOrderQuery'
      - $ref: '#/components/parameters/SortByQueryUser'
      - $ref: '#/components/parameters/OffsetQuery'
      - $ref: '#/components/parameters/SizeQuery'
      responses:
        '200':
          $ref: '#/components/responses/UserListingResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    post:
      tags:
      - User Management
      summary: Create User (V2)
      operationId: CreateUserV2
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    put:
      tags:
      - User Management
      summary: Update User (V2)
      operationId: UpdateUserV2
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user:
    get:
      tags:
      - User Management
      summary: List All Users (V1)
      operationId: GetAllUsers
      responses:
        '200':
          description: List of all users.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfo'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    post:
      tags:
      - User Management
      summary: Create User
      operationId: CreateUser
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    put:
      tags:
      - User Management
      summary: Update User
      operationId: UpdateUser
      requestBody:
        $ref: '#/components/requestBodies/UserInfo'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '406':
          $ref: '#/components/responses/NotAcceptable'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/{id}:
    get:
      tags:
      - User Management
      summary: Get User by ID
      operationId: GetUserById
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    delete:
      tags:
      - User Management
      summary: Delete User by ID
      operationId: DeleteUser
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/v2/{id}:
    get:
      tags:
      - User Management
      summary: Get User by ID (V2)
      operationId: GetUserByIdV2
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/UserInfoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/bulk:
    delete:
      tags:
      - User Management
      summary: Bulk Delete Users
      operationId: BulkDeleteUsers
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/detail/get:
    get:
      tags:
      - User Management
      summary: List All Detailed Users
      operationId: GetAllDetailedUsers
      responses:
        '200':
          description: List of all users with detailed information.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfo'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/group/v2:
    get:
      tags:
      - Role Group Management
      summary: List Role Groups (V2 - Paginated, Filtered)
      operationId: FetchRoleGroupsV2
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/SearchKeyQuery'
      - $ref: '#/components/parameters/SortOrderQuery'
      - $ref: '#/components/parameters/SortByQueryRoleGroup'
      - $ref: '#/components/parameters/OffsetQuery'
      - $ref: '#/components/parameters/SizeQuery'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupListingResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    post:
      tags:
      - Role Group Management
      summary: Create Role Group (V2)
      operationId: CreateRoleGroupV2
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    put:
      tags:
      - Role Group Management
      summary: Update Role Group (V2)
      operationId: UpdateRoleGroupV2
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/group:
    get:
      tags:
      - Role Group Management
      summary: List All Role Groups (V1)
      operationId: FetchRoleGroups
      responses:
        '200':
          description: List of all role groups.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleGroup'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    post:
      tags:
      - Role Group Management
      summary: Create Role Group
      operationId: CreateRoleGroup
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    put:
      tags:
      - Role Group Management
      summary: Update Role Group
      operationId: UpdateRoleGroup
      requestBody:
        $ref: '#/components/requestBodies/RoleGroup'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/group/{id}:
    get:
      tags:
      - Role Group Management
      summary: Get Role Group by ID
      operationId: FetchRoleGroupById
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
    delete:
      tags:
      - Role Group Management
      summary: Delete Role Group by ID
      operationId: DeleteRoleGroup
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/group/v2/{id}:
    get:
      tags:
      - Role Group Management
      summary: Get Role Group by ID (V2)
      operationId: FetchRoleGroupByIdV2
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - $ref: '#/components/parameters/PathId'
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/group/detailed/get:
    get:
      tags:
      - Role Group Management
      summary: List All Detailed Role Groups
      operationId: FetchDetailedRoleGroups
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupListingResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/group/search:
    get:
      tags:
      - Role Group Management
      summary: Search Role Groups by Name
      operationId: FetchRoleGroupsByName
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: name
        in: query
        required: true
        description: Name of the role group to search for.
        schema:
          type: string
      responses:
        '200':
          $ref: '#/components/responses/RoleGroupResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/group/bulk:
    delete:
      tags:
      - Role Group Management
      summary: Bulk Delete Role Groups
      operationId: BulkDeleteRoleGroups
      responses:
        '200':
          $ref: '#/components/responses/GenericSuccess'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/check/roles:
    get:
      tags:
      - Authentication
      summary: Check Logged-in User's Roles
      operationId: CheckUserRoles
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: appName
        in: query
        required: false
        description: Optional application name to filter roles for.
        schema:
          type: string
      responses:
        '200':
          description: User roles information.
          content:
            application/json:
              schema:
                type: object
                properties:
                  role:
                    type: string
                    nullable: true
                  roles:
                    type: array
                    items:
                      type: string
                  superAdmin:
                    type: boolean
                    nullable: true
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/sync/orchestratortocasbin:
    get:
      tags:
      - Policy Management
      summary: Sync Orchestrator to Casbin
      operationId: SyncOrchestratorToCasbin
      description: Synchronizes policies from orchestrator to Casbin. Requires admin
        privileges.
      responses:
        '200':
          description: Sync status.
          content:
            application/json:
              schema:
                type: boolean
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/update/trigger/terminal:
    put:
      tags:
      - Policy Management
      summary: Update Trigger Policy for Terminal Access
      operationId: UpdateTriggerPolicyForTerminalAccess
      description: Updates trigger policies related to terminal access. Requires global
        update privileges.
      responses:
        '200':
          description: Policy update status.
          content:
            application/json:
              schema:
                type: string
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security: []
      
  /orchestrator/user/role/cache:
    get:
      tags:
      - Cache Management
      summary: Get Role Cache Dump
      operationId: GetRoleCacheDump
      description: Retrieves a dump of the role cache. Requires super admin privileges.
      responses:
        '200':
          description: Cache dump data.
          content:
            application/json:
              schema:
                type: object
        '403':
          $ref: '#/components/responses/Forbidden'
      security: []
      
  /orchestrator/user/role/cache/invalidate:
    get:
      tags:
      - Cache Management
      summary: Invalidate Role Cache
      operationId: InvalidateRoleCache
      description: Clears the role cache. Requires super admin privileges.
      responses:
        '200':
          description: Cache invalidation status.
          content:
            application/json:
              schema:
                type: string
        '403':
          $ref: '#/components/responses/Forbidden'
      security: []
      
  /orchestrator/login:
    get:
      tags:
      - Authentication
      summary: OIDC Login Redirect (Informational)
      description: Initiates OIDC login flow. Handled by the OIDC client library.
      operationId: HandleOIDCLogin
      responses:
        '302':
          description: Redirect to OIDC provider.
      security: []
      
  /orchestrator/auth/login:
    get:
      tags:
      - Authentication
      summary: OIDC Login Redirect (Informational - Alternate Path)
      description: Initiates OIDC login flow. Handled by the OIDC client library.
      operationId: HandleOIDCAuthLogin
      responses:
        '302':
          description: Redirect to OIDC provider.
      security: []
      
  /orchestrator/auth/callback:
    get:
      tags:
      - Authentication
      summary: OIDC Callback (Informational)
      description: Handles the callback from the OIDC provider. Handled by the OIDC
        client library.
      operationId: HandleOIDCCallback
      responses:
        '302':
          description: Redirect after successful authentication.
        '400':
          description: Error during OIDC callback.
        '500':
          description: Server error during OIDC callback.
      security: []
      
  /orchestrator/api/dex/{path}:
    get:
      tags:
      - Authentication
      summary: Dex Proxy (Informational)
      description: Proxies requests to the Dex IdP. Handled by the Dex proxy mechanism.
      operationId: DexProxyHandler
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: path
        in: path
        required: true
        description: Path to be proxied to Dex.
        schema:
          type: string
      responses:
        default:
          description: Response from Dex.
      security: []
      
  /orchestrator/env:
    post:
      summary: Create Environment
      description: Create a new environment within a cluster.
      operationId: CreateEnvironment
      security: []
      
      tags:
      - Environment Management
      requestBody:
        description: Environment details
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnvironmentCreateRequest'
      responses:
        '200':
          description: Successfully created environment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentDetail'
        '400':
          description: Bad Request (e.g., validation error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update Environment
      description: Update an existing environment.
      operationId: UpdateEnvironment
      security: []
      
      tags:
      - Environment Management
      requestBody:
        description: Environment details to update
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnvironmentUpdateRequest'
      responses:
        '200':
          description: Successfully updated environment
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentDetail'
        '400':
          description: Bad Request (e.g., validation error, or ID not found)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      summary: Get Environment by ID
      description: Get detailed information for a specific environment by its ID.
      operationId: GetEnvironmentById
      security: []
      
      tags:
      - Environment Management
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: id
        in: query
        required: true
        description: ID of the environment
        schema:
          type: integer
      responses:
        '200':
          description: Successfully retrieved environment details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnvironmentDetail'
        '400':
          description: Bad Request (e.g., invalid ID)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Environment not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/delete:
    post:
      summary: Delete Cluster
      description: Delete an existing cluster.
      operationId: DeleteCluster
      security: []
      tags:
      - Cluster Management
      requestBody:
        description: A JSON object containing the cluster config (primarily ID is
          used for deletion)
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClusterBean'
      responses:
        '200':
          description: Successfully deleted the cluster
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Cluster deleted successfully.
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/env/delete:
    post:
      summary: Delete Environment (via POST)
      description: Delete an existing environment using POST method.
      operationId: DeleteEnvironmentViaPost
      security: []
      tags:
      - Environment Management
      requestBody:
        description: A JSON object containing the env config (primarily ID is used
          for deletion)
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnvironmentDetail'
      responses:
        '200':
          description: Successfully deleted the environment
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Environment deleted successfully.
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/env/clusters:
    get:
      summary: List clusters with their environments
      description: Provides a list of all clusters and the environments within each.
      operationId: getClustersWithEnvironments
      security: []
      
      tags:
      - Cluster Environment
      responses:
        '200':
          description: Successfully retrieved list of clusters and environments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClusterWithEnvironments'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/{cluster_id}/env:
    get:
      summary: List environments for a specific cluster
      description: Provides a list of all environments for a given cluster ID.
      operationId: getEnvironmentsForCluster
      security: []
      
      tags:
      - Cluster Environment
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: cluster_id
        in: path
        required: true
        description: ID of the cluster
        schema:
          type: integer
      responses:
        '200':
          description: Successfully retrieved list of environments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Environment'
        '400':
          description: Bad Request. Invalid cluster ID.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Cluster not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster:
    put:
      summary: Update Cluster
      description: Update an existing cluster's configuration.
      operationId: UpdateCluster
      security: []
      
      tags:
      - Cluster Management
      requestBody:
        description: A JSON object containing the cluster config
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClusterBean'
      responses:
        '200':
          description: Successfully updated the cluster
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterBean'
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      summary: Get Cluster
      description: Get details of a specific cluster by ID.
      operationId: GetCluster
      security: []
      
      tags:
      - Cluster Management
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: id
        in: query
        description: cluster id.
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: Successfully get cluster
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterBean'
        '400':
          description: Bad Request. Input Validation(decode) error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/cluster/auth-list:
    get:
      summary: List Accessible Clusters
      description: list of clusters accessible to the authenticated user.
      operationId: GetAuthClusterList
      security: []
      
      tags:
      - Cluster Management
      responses:
        '200':
          description: cluster list
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: status code
                  status:
                    type: string
                    description: status
                  result:
                    type: array
                    description: namespace list group by cluster
                    items:
                      $ref: '#/components/schemas/ClusterAuthDetail'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orchestrator/cluster/validate:
    post:
      summary: Validate Cluster Configuration
      description: Validate a cluster configuration using kubeconfig.
      operationId: ValidateCluster
      security: []
      tags:
      - Cluster Management
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                kubeconfig:
                  $ref: '#/components/schemas/Kubeconfig'
              required:
              - kubeconfig
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateClusterBean'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /cluster/saveClusters:
    post:
      summary: Save Multiple Clusters
      description: Save configurations for multiple clusters.
      operationId: SaveClusters
      security: []
      
      tags:
      - Cluster Management
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ValidateClusterBean'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ValidateClusterBean'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /app/env/patch:
    patch:
      summary: Patch Application Environment
      description: change the deployment template for an app and environment
      operationId: PatchAppEnv
      security: []
      
      requestBody:
        content:
          application/json:
            schema:
              properties:
                envId:
                  type: integer
                  description: Environment Id
                appId:
                  type: integer
                  description: Application Id
                targetChartRefId:
                  type: integer
                  description: Chart ref Id of template
      responses:
        '200':
          description: patched data
        '422':
          description: bad request
      tags:
      - Change Chart
  /app/workflow/clone:
    post:
      summary: Clone Application Workflow
      description: Clones an application workflow from a source environment to a target
        environment
      operationId: CloneApplicationWorkflow
      security: []
      
      requestBody:
        description: A JSON object containing the details required to clone the workflow
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CloneApplicationWorkflowRequest'
      responses:
        '200':
          description: Cloning operation response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StandardResponse'
      tags:
      - Clone Workflow
  /k8s/resource:
    post:
      summary: Get Resource Manifest
      description: This API is used for fetching the manifest of a specified Kubernetes
        resource.
      operationId: getResourceManifest
      security: []
      
      requestBody:
        description: JSON payload specifying the resource to fetch.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully retrieved the resource manifest.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
      - K8s Resource
    put:
      summary: Update Resource Manifest
      description: This API is used for editing the manifest of a specified Kubernetes
        resource.
      operationId: updateResourceManifest
      security: []
      
      requestBody:
        description: JSON payload containing the resource identifier and the patch.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully updated the resource manifest.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
      - K8s Resource
  /k8s/resource/create:
    post:
      summary: Create Resource
      description: This API is used for applying a desired manifest to create a Kubernetes
        resource.
      operationId: createResource
      security: []
      
      requestBody:
        description: JSON payload containing the resource manifest to apply.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully created the resource.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
      - K8s Resource
  /k8s/resource/delete:
    post:
      summary: Delete Resource
      description: This API is used for deleting a specified Kubernetes resource.
      operationId: deleteResource
      security: []
      
      requestBody:
        description: JSON payload specifying the resource to delete.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully deleted the resource.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    $ref: '#/components/schemas/ResourceGetResponse'
      tags:
      - K8s Resource
  /k8s/events:
    post:
      summary: Get Resource Events
      description: This API is used for fetching events for Kubernetes resources.
      operationId: getResourceEvents
      security: []
      
      requestBody:
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully retrieved resource events. The response is a stream
            of events.
          content:
            text/event-stream:
              schema:
                $ref: '#/components/schemas/EventsResponseObject'
      tags:
      - K8s Resource
  /k8s/pods/logs/{podName}:
    get:
      summary: Get Pod Logs
      description: This API is used for fetching logs for a specified container within
        a pod.
      operationId: getPodLogs
      security: []
      
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: podName
        in: path
        required: true
        description: Name of the pod.
        schema:
          type: string
      - name: containerName
        in: query
        required: true
        description: Name of the container within the pod.
        schema:
          type: string
      - name: appId
        in: query
        required: false
        description: Application ID.
        schema:
          type: string
      - name: clusterId
        in: query
        required: false
        description: Cluster ID.
        schema:
          type: integer
      - name: namespace
        in: query
        description: Namespace of the pod. Required if clusterId is passed.
        required: false
        schema:
          type: string
      - name: follow
        in: query
        description: Whether to follow the log stream.
        schema:
          type: boolean
          default: false
      - name: sinceSeconds
        in: query
        description: Return logs newer than a relative duration like 5s, 2m, or 3h.
          Defaults to all logs.
        schema:
          type: integer
      - name: tailLines
        in: query
        description: If set, the number of lines from the end of the logs to show.
        schema:
          type: integer
      responses:
        '200':
          description: Successfully retrieved pod logs. The response is a stream of
            log lines.
          content:
            text/event-stream:
              schema:
                $ref: '#/components/schemas/LogsResponseObject'
      tags:
      - K8s Resource
  /k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}:
    get:
      summary: Get Pod Exec Session
      description: This API establishes a session for executing commands in a pod's
        container (terminal access).
      operationId: getPodExecSession
      security: []
      
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: identifier
        in: path
        required: true
        description: Application ID or Cluster ID. Example '2|devtroncd|devtron' or
          '3'.
        schema:
          type: string
      - name: namespace
        in: path
        required: true
        description: Namespace of the pod.
        schema:
          type: string
          example: devtroncd
      - name: pod
        in: path
        required: true
        description: Name of the pod.
        schema:
          type: string
          example: inception-58d44d99fd-tfw4s
      - name: shell
        in: path
        required: true
        description: Shell to invoke.
        schema:
          type: string
          enum:
          - bash
          - sh
          - powershell
          - cmd
          example: bash
      - name: container
        in: path
        required: true
        description: Name of the container.
        schema:
          type: string
          example: devtron
      responses:
        '200':
          description: Successfully established exec session.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TerminalMessage'
      tags:
      - K8s Resource
  /k8s/api-resources/{clusterId}:
    get:
      summary: Get API Resources
      description: Get all available API resources for a given cluster ID.
      operationId: getApiResources
      security: []
      
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: clusterId
        in: path
        description: ID of the cluster.
        required: true
        schema:
          type: integer
          format: int64
      responses:
        '200':
          description: Successfully fetched all API resources for the given cluster
            ID.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllApiResourcesResponse'
      tags:
      - K8s Resource
  /k8s/resource/list:
    post:
      summary: List Resources
      description: This API is used for fetching a list of Kubernetes resources based
        on the request criteria.
      operationId: listResources
      security: []
      
      requestBody:
        description: JSON payload specifying the criteria for listing resources.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequestObject'
      responses:
        '200':
          description: Successfully retrieved the list of resources.
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: Status code of the response.
                  status:
                    type: string
                    description: Status message of the response.
                  result:
                    type: array
                    description: List of resources.
                    items:
                      $ref: '#/components/schemas/ClusterResourceListResponse'
      tags:
      - K8s Resource
  /k8s/resources/rotate:
    post:
      summary: Rotates the Pods
      description: This API is used to rotate (restart) pods for the provided resources.
      operationId: K8sResourceForResources
      security: []
      
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: appId
        in: query
        description: Application ID.
        required: true
        schema:
          type: string
      requestBody:
        description: JSON payload specifying the resources for which pods should be
          rotated.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RotatePodRequest'
      responses:
        '200':
          description: Response array indicating the status of rotation for each resource.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RotatePodResponse'
      tags:
      - K8s Resource
  /k8s/resources/apply:
    post:
      summary: Apply Resources
      description: This API is used to apply (create or update) Kubernetes resources
        in a cluster.
      operationId: applyResources
      security: []
      
      requestBody:
        description: JSON payload containing the manifest of resources to apply.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplyResourcesRequest'
      responses:
        '200':
          description: Response array indicating the status of application for each
            resource.
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ApplyResourcesResponse'
      tags:
      - K8s Resource
  /app/workflow:
    post:
      summary: Create an application workflow
      description: Creates a new workflow for a given application.
      operationId: CreateAppWorkflow
      security: []
      
      tags:
      - Workflow Management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AppWorkflowDto'
      responses:
        '200':
          description: Successfully created workflow.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppWorkflowDto'
        '400':
          description: Bad Request. Validation error or wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden. User does not have permission.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /app/workflow/{app-wf-id}/app/{app-id}:
    delete:
      summary: Delete an application workflow
      description: Deletes an existing workflow for a given application.
      operationId: DeleteAppWorkflow
      security: []
      
      tags:
      - Workflow Management
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: app-wf-id
        in: path
        required: true
        description: ID of the application workflow to delete.
        schema:
          type: integer
      - name: app-id
        in: path
        required: true
        description: ID of the application.
        schema:
          type: integer
      responses:
        '200':
          description: Successfully deleted workflow.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: OK
        '400':
          description: Bad Request. Invalid path parameters.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized User.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden. User does not have permission.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /version:
    get:
      summary: Get Devtron server version information
      security: []
      responses:
        '200':
          description: meta info about devtron server
          content:
            application/json:
              schema:
                type: object
                properties:
                  gitCommit:
                    type: string
                    example: d252aa3e
                    description: git hash from which code was compiled
                  buildTime:
                    type: string
                    format: date-time
                    example: '2021-12-15T05:44:05Z'
                    description: time when code was complied
                  serverMode:
                    type: string
                    example: FULL
                    description: server mode FULL/EA_ONLY
                    enum:
                    - FULL
                    - EA_ONLY
      tags:
      - Devtron Server version
  /validate:
    post:
      description: Validate gitops configuration by dry run
      summary: Validate gitops configuration by dry run
      security: []
      operationId: GitOpsValidateDryRun
      requestBody:
        description: A JSON object containing the gitops configuration
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GitOpsConfigDto'
      responses:
        '200':
          description: Successfully return all validation stages results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedError'
        '400':
          description: Bad Request. Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      tags:
      - GitOps Validation
  /config:
    post:
      description: create/save new configuration and validate them before saving
      summary: create/save new configuration and validate them before saving
      security: []
      operationId: CreateGitOpsConfig
      requestBody:
        description: A JSON object containing the gitops configuration
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GitOpsConfigDto'
      responses:
        '200':
          description: Successfully return all validation stages results and if validation
            is correct then saves the configuration in the backend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedError'
        '400':
          description: Bad Request. Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      tags:
      - GitOps Validation
    put:
      description: update configuration and validate them before saving(if last validation
        is within 30 seconds then do not validate)
      summary: update configuration and validate them before saving(if last validation
        is within 30 seconds then do not validate)
      operationId: UpdateGitOpsConfig
      security: []
      requestBody:
        description: A JSON object containing the gitops configuration
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GitOpsConfigDto'
      responses:
        '200':
          description: Successfully return all validation stages results and if validation
            is correct then updates the configuration in the backend
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedError'
        '400':
          description: Bad Request. Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      tags:
      - GitOps Validation
  /notification:
    get:
      summary: Returns all notification settings
      description: Returns all notification settings
      operationId: findNotificationSetting
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: offset
        in: query
        description: value can be regex search string.
        required: true
        schema:
          type: integer
      - name: size
        in: query
        description: value can be regex search string.
        required: true
        schema:
          type: integer
      security: []
      
      tags:
      - Notifications
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationSetting'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Creates a new NotificationSetting
      description: create NotificationSetting api.
      operationId: addNotificationSetting
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationSetting'
      security: []
      
      tags:
      - Notifications
      responses:
        '200':
          description: create NotificationSetting response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationSetting'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update  NotificationSetting
      description: Update NotificationSetting api either recipients or events(trigger/success/failed).
      operationId: updateNotificationSetting
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationSetting'
      security: []
      
      tags:
      - Notifications
      responses:
        '200':
          description: create NotificationSetting response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationSetting'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    delete:
      summary: delete NotificationSetting
      description: delete NotificationSetting.
      operationId: deleteNotificationSetting
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationSetting'
      security: []
      
      tags:
      - Notifications
      responses:
        '200':
          description: create NotificationSetting response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationSetting'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /notification/recipient:
    get:
      summary: used to fetch providers(recipients)
      description: recipients fetch by string search, it will return slacks providers
        and email ids
      operationId: deleteGroupPolicy
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: value
        in: query
        description: value can be regex search string.
        required: true
        schema:
          type: string
      security: []
      
      tags:
      - Notifications
      responses:
        '204':
          description: list of recipients
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /notification/channel:
    get:
      summary: get all NotificationSettingConfig list
      description: get all NotificationSettingConfig list
      operationId: findNotificationSettingConfig
      security: []
      
      tags:
      - Notifications
      responses:
        '200':
          description: list response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationConfigResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Creates a new NotificationSettingConfig
      description: create NotificationSettingConfig, Slack or SES
      operationId: addNotificationSettingConfig
      requestBody:
        description: json as request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NotificationConfig'
      security: []
      
      tags:
      - Notifications
      responses:
        '200':
          description: create NotificationSettingConfig response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationConfigResponse'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/app/history/deployed-component/detail/{appId}/{pipelineId}/{id}:
    get:
      summary: Fetch component history
      description: fetch detail of a history on the basis of the history component
        and it's name
      operationId: FetchHistoryByDeployedConfigurationDetail
      tags:
      - Deployment History
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: appId
        in: path
        required: true
        schema:
          type: integer
      - name: pipelineId
        in: path
        required: true
        schema:
          type: integer
      - name: id
        in: path
        required: true
        schema:
          type: integer
      - name: historyComponent
        in: query
        required: true
        schema:
          type: string
          enum:
          - DEPLOYMENT_TEMPLATE
          - CONFIGMAP
          - SECRET
          - PIPELINE_STRATEGY
      - name: historyComponentName
        in: query
        required: false
        description: name of config-map, secret
        schema:
          type: string
      security: []
      
      responses:
        '200':
          description: Successfully return history
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HistoryComponentDetailDto'
        '400':
          description: Bad Request. Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/app/history/deployed-component/list/{appId}/{pipelineId}:
    get:
      summary: fetch deployed history details list
      description: fetch deployed history details list
      operationId: FetchHistoryListByDeployedConfigurationDetail
      tags:
      - Deployment History
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: appId
        in: path
        required: true
        schema:
          type: integer
      - name: pipelineId
        in: path
        required: true
        schema:
          type: integer
      - name: historyComponent
        in: query
        required: true
        schema:
          type: string
          enum:
          - DEPLOYMENT_TEMPLATE
          - CONFIGMAP
          - SECRET
          - PIPELINE_STRATEGY
      - name: historyComponentName
        in: query
        required: false
        description: name of config-map, secret
        schema:
          type: string
      - name: baseConfigurationId
        in: query
        required: true
        description: id of base configuration
        schema:
          type: integer
      security: []
      
      responses:
        '200':
          description: Successfully return history list
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HistoryComponentListDto'
        '400':
          description: Bad Request. Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /orchestrator/app/history/deployed-configuration/{appId}/{pipelineId}/{wfrId}:
    get:
      summary: all deployed configuration history
      description: fetch all deployed configurations history (deployment template,
        pipeline strategy, configmaps, secrets)
      operationId: FetchHistoryListForAllDeployedConfigurations
      tags:
      - Deployment History
      parameters:
      - name: token
        in: header
        required: true
        description: Authentication token.
        schema:
          type: string
      - name: appId
        in: path
        required: true
        schema:
          type: integer
      - name: pipelineId
        in: path
        required: true
        schema:
          type: integer
      - name: wfrId
        in: path
        required: true
        schema:
          type: integer
      security: []
      
      responses:
        '200':
          description: Successfully return history
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/HistoryConfigurationListDto'
        '400':
          description: Bad Request. Input Validation error/wrong request body.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Unauthorized User
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    AppLabel:
      type: object
      required:
      - key
      - value
      properties:
        key:
          type: string
          description: label key
        value:
          type: string
          description: label value
        propagate:
          type: boolean
          description: Whether to propagate to kubernetes resources
    AppLabels:
      type: object
      required:
      - appId
      - labels
      properties:
        appId:
          type: integer
          description: application id
        labels:
          type: array
          items:
            $ref: '#/components/schemas/AppLabel'
    AppMetaInfo:
      type: object
      required:
      - appId
      - projectId
      - appName
      - projectName
      - createdOn
      - createdBy
      - labels
      properties:
        appId:
          type: integer
          description: app id
        projectId:
          type: integer
          description: team/project id
        appName:
          type: string
          description: app name
        projectName:
          type: string
          description: team/project name
        labels:
          type: array
          items:
            $ref: '#/components/schemas/AppLabel'
        createdOn:
          type: string
          description: app creation date
        createdBy:
          type: string
          description: app created by
    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          format: int32
        status:
          type: string
        result:
          type: object
          nullable: true
        errors:
          type: array
          items:
            type: object
            properties:
              userMessage:
                type: string
                nullable: true
              internalMessage:
                type: string
                nullable: true
    Error:
      required:
      - code
      - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message
    CreateJob:
      type: object
      properties:
        appName:
          type: string
          description: Used to give the name of the job
          example: my-job-1
        isJob:
          type: boolean
          description: States whether its a job or an app
          example: true
        teamId:
          type: integer
          description: Used to give project id
          example: 1
        templateId:
          type: integer
          description: Used to give the id of the job it wants to clone
          example: 18
        labels:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                example: hello
              value:
                type: string
                example: world
              propogate:
                type: boolean
                example: false
        description:
          type: string
          description: Used to give the description of the job once it is made.
          example: This is my first Job
    ActionResponse:
      type: object
      properties:
        id:
          type: integer
          description: Used to give the id of job once its created
          example: 25
        appName:
          type: string
          description: Used to give the name of job once its created
          example: my-job-1
        material:
          type: array
          items:
            $ref: '#/components/schemas/gitMaterial'
        teamId:
          type: integer
          description: Used to give the team id
          example: 1
        templateId:
          type: integer
          description: Used to give the templateId
          example: 0
        description:
          type: string
          description: Used to give the description of the job once it is made.
          example: This is my first Job
        isJob:
          type: boolean
          description: used to tell whether it is a job or an app
          example: true
    JobList:
      type: object
      properties:
        teams:
          type: array
          items:
            type: integer
          description: used to give the project id
          example:
          - 1
          - 2
        appStatuses:
          type: array
          items:
            type: string
          description: used to give the filter of app ci-build status
          example:
          - Succeeded
          - Starting
        sortBy:
          type: string
          description: used to give the sort by constraint
          example: appNameSort
        sortOrder:
          type: string
          description: used to give the sort order
          example: ASC
        offset:
          type: integer
          description: used to give the number from which we want our job (if the
            offset is 20 means we want list of jobs from 20)
          example: 0
        size:
          type: integer
          description: used to give the number of jobs we want
          example: 20
    JobListResponse:
      type: object
      properties:
        jobContainers:
          type: array
          items:
            $ref: '#/components/schemas/jobContainer'
        jobCount:
          type: integer
    gitMaterial:
      type: object
      properties:
        name:
          type: string
        url:
          type: string
        id:
          type: integer
        gitProviderId:
          type: integer
        checkoutPath:
          type: string
        fetchSubmodules:
          type: boolean
        isUsedInCiConfig:
          type: boolean
    jobContainer:
      type: object
      properties:
        jobId:
          type: integer
        jobName:
          type: string
        description:
          type: string
        ciPipelines:
          type: array
          items:
            $ref: '#/components/schemas/jobCiPipeline'
    jobCiPipeline:
      type: object
      properties:
        ciPipelineId:
          type: integer
        status:
          type: string
        lastRunAt:
          type: string
          format: date-time
        lastSuccessAt:
          type: string
          format: date-time
    ChartInfo:
      type: object
      required:
      - installedAppId
      - environmentId
      - installedAppVersionId
      - appStoreApplicationVersionId
      - appStoreApplicationName
      - status
      - appName
      - environmentName
      - deployedAt
      - deployedBy
      - readme
      - deprecated
      properties:
        installedAppId:
          type: integer
          description: installed chart id
        environmentId:
          type: integer
          description: environment id
        installedAppVersionId:
          type: integer
          description: installed chart version id
        appStoreApplicationVersionId:
          type: integer
          description: team/project id
        appStoreApplicationName:
          type: string
          description: chart name externally
        chartName:
          type: string
          description: chart repo name
        icon:
          type: string
          description: image
        status:
          type: string
          description: status of deployed chart
        appName:
          type: string
          description: chart name is app name for devtron
        environmentName:
          type: string
          description: env name
        deployedAt:
          type: string
          description: deployement time
        deployedBy:
          type: string
          description: user
        readme:
          type: string
          description: readme
        deprecated:
          type: boolean
          description: is deprecated or not
    App:
      type: object
      required:
      - appName
      - teamId
      - templateId
      properties:
        appName:
          type: string
          description: app name
        teamId:
          type: integer
          description: project id
        templateId:
          type: integer
          description: reference app id, used for clone, set default value 0 for blank
            app.
        labels:
          type: array
          description: each apps may have multiple labels. these are optional.
          items:
            $ref: '#/components/schemas/AppLabel'
    AppProjectUpdateRequest:
      type: object
      required:
      - teamId
      - appId
      properties:
        teamId:
          type: integer
          description: project id
        appId:
          type: array
          description: team id, teams ids are projects ids
          items:
            type: integer
    AppListingRequest:
      type: object
      required: []
      properties:
        appNameSearch:
          type: string
          description: app name search, wildcard match
        offset:
          type: integer
          description: offset
        size:
          type: integer
          description: result size
        sortBy:
          type: string
          description: sort by
        sortOrder:
          type: string
          description: sort order
        environments:
          type: array
          description: environment id
          items:
            type: integer
        teams:
          type: array
          description: team id, teams ids are projects ids
          items:
            type: integer
        labels:
          type: array
          description: app labels
          items:
            type: string
        statuses:
          type: array
          description: status
          items:
            type: string
    AppContainer:
      type: object
      required:
      - appId
      - appName
      - environments
      properties:
        appId:
          type: integer
          description: app id
        appName:
          type: string
          description: app name
        environments:
          type: array
          items:
            $ref: '#/components/schemas/EnvContainer'
    EnvContainer:
      type: object
      required:
      - appId
      - appName
      - environmentId
      - environmentName
      properties:
        appId:
          type: integer
          description: app id
        appName:
          type: string
          description: app name
        cdStageStatus:
          type: string
          description: app name
        dataSource:
          type: string
          description: app name
        ciArtifactId:
          type: integer
          description: app name
        deleted:
          type: boolean
          description: app name
        environmentId:
          type: integer
          description: app name
        environmentName:
          type: string
          description: app name
        status:
          type: string
          description: app name
        appStatus:
          type: string
          description: app status for this environment
        postStageStatus:
          type: string
          description: app name
        preStageStatus:
          type: string
          description: app name
        lastDeployedTime:
          type: string
          description: deployed time
        materialInfo:
          type: array
          items:
            type: object
    DeploymentGroup:
      type: object
      required:
      - id
      properties:
        id:
          type: integer
          description: id
        ciPipelineId:
          type: integer
          description: ciPipelineId
        environmentId:
          type: integer
          description: environmentId
        appCount:
          type: integer
          description: appCount
        name:
          type: string
          description: name
        noOfApps:
          type: string
          description: noOfApps
    BulkUpdateSeeExampleResponse:
      type: object
      required:
      - Script
      properties:
        resource:
          type: string
          description: Resource from url path, i.e. {apiVersion} & {kind}
        script:
          $ref: '#/components/schemas/BulkUpdateScript'
          description: Input Script for bulk update
        readMe:
          type: string
          description: Readme for bulk update
    BulkUpdateScript:
      type: object
      required:
      - ApiVersion
      - Kind
      - Spec
      properties:
        apiVersion:
          type: string
          description: Api version from url
          example:
          - v1beta1
        kind:
          type: string
          description: Kind
          example:
          - application
        spec:
          $ref: '#/components/schemas/BulkUpdatePayload'
    BulkUpdatePayload:
      type: object
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
          description: All Env Id's for updating dependent apps
        global:
          type: boolean
          description: Global flag for updating dependent apps
        DeploymentTemplate:
          $ref: '#/components/schemas/Tasks'
        ConfigMaps:
          type: object
          properties:
            names:
              type: array
              items:
                type: string
                description: Name of All ConfigMaps to be updated
            tasks:
              $ref: '#/components/schemas/Spec'
        Secrets:
          type: object
          properties:
            names:
              type: array
              items:
                type: string
                description: Name of All Secrets to be updated
            tasks:
              $ref: '#/components/schemas/Spec'
    Tasks:
      type: object
      properties:
        spec:
          $ref: '#/components/schemas/Spec'
          description: Spec of the Task
    Spec:
      type: object
      properties:
        patchData:
          type: string
          description: string with details of the patch to be used for updating
    NameIncludesExcludes:
      type: object
      properties:
        names:
          type: array
          items:
            type: string
          description: All strings of app names to be included/excluded
    ImpactedObjectsResponse:
      type: object
      properties:
        deploymentTemplate:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateImpactedObjectsResponseForOneApp'
        configMap:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
        secret:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretImpactedObjectsResponseForOneApp'
    DeploymentTemplateImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
    CmAndSecretImpactedObjectsResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the impacted app
        appName:
          type: string
          description: Name of the impacted app
        envId:
          type: string
          description: Env Id of the impacted app
        names:
          type: array
          items:
            type: string
            description: Names of all configmaps/secrets impacted
    BulkUpdateResponse:
      type: object
      properties:
        deploymentTemplate:
          $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponse'
        configMap:
          $ref: '#/components/schemas/CmAndSecretBulkUpdateResponse'
        secret:
          $ref: '#/components/schemas/CmAndSecretBulkUpdateResponse'
    DeploymentTemplateBulkUpdateResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: All top-level messages in response of bulk update action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/DeploymentTemplateBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update applied successfully
    CmAndSecretBulkUpdateResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: All top-level messages in response of bulk update action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/CmAndSecretBulkUpdateResponseForOneApp'
          description: Details of all apps on which bulk update applied successfully
    DeploymentTemplateBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the concerned app
        appName:
          type: string
          description: Name of the concerned app
        envId:
          type: integer
          description: Env ID of the concerned app
        message:
          type: string
          description: App-level message for the concerned app
    CmAndSecretBulkUpdateResponseForOneApp:
      type: object
      properties:
        appId:
          type: integer
          description: Id of the concerned app
        appName:
          type: string
          description: Name of the concerned app
        envId:
          type: integer
          description: Env ID of the concerned app
        names:
          type: array
          items:
            type: string
          description: Names of all configmaps/secrets
        message:
          type: string
          description: App-level message for the concerned app
    BulkActionRequest:
      type: object
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
          description: All Env Id's for the bulk action
        appIds:
          type: array
          items:
            type: integer
          description: All App Id's for the bulk action (alternative to includes/excludes
            by name)
        projectIds:
          type: array
          items:
            type: integer
          description: All Project Id's for the bulk action
    BulkActionResponse:
      type: object
      properties:
        message:
          type: array
          items:
            type: string
          description: Top-level messages in response of the bulk action
        failure:
          type: array
          items:
            $ref: '#/components/schemas/BulkActionFailureDetail'
          description: Details of all items on which the bulk action failed
        successful:
          type: array
          items:
            $ref: '#/components/schemas/BulkActionSuccessDetail'
          description: Details of all items on which the bulk action applied successfully
    BulkActionFailureDetail:
      type: object
      properties:
        appId:
          type: integer
        appName:
          type: string
        envId:
          type: integer
        message:
          type: string
    BulkActionSuccessDetail:
      type: object
      properties:
        appId:
          type: integer
        appName:
          type: string
        envId:
          type: integer
        message:
          type: string
    BulkDeployRequest:
      type: object
      required:
      - envIds
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        envIds:
          type: array
          items:
            type: integer
          description: All Env Id's for the bulk deployment
        appIds:
          type: array
          items:
            type: integer
          description: All App Id's for the bulk deployment (alternative to includes/excludes
            by name)
        projectIds:
          type: array
          items:
            type: integer
          description: All Project Id's for the bulk deployment
        artifactId:
          type: integer
          description: ID of the CI artifact to be deployed
        releaseId:
          type: integer
          description: ID of the release to be deployed
        deploymentStrategy:
          type: string
          description: Deployment strategy to use (e.g., blue-green, canary, recreate)
    BulkBuildTriggerRequest:
      type: object
      required:
      - ciPipelineId
      properties:
        includes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        excludes:
          $ref: '#/components/schemas/NameIncludesExcludes'
        appIds:
          type: array
          items:
            type: integer
          description: All App Id's for the bulk build trigger (alternative to includes/excludes
            by name)
        projectIds:
          type: array
          items:
            type: integer
          description: All Project Id's for the bulk build trigger
        ciPipelineId:
          type: integer
          description: ID of the CI pipeline to trigger builds for
    SSOLoginDto:
      type: object
      properties:
        id:
          type: integer
          format: int32
          readOnly: true
        name:
          type: string
          description: Type of auth ie google/github
        label:
          type: string
          nullable: true
        url:
          type: string
          format: url
          description: Devtron Dashboard Url
          example: https://devtron.example.com/orchestrator
          nullable: true
        config:
          type: object
          description: Configuration for the SSO provider (Dex connector config).
            Structure varies.
        active:
          type: boolean
      required:
      - name
      - url
      - config
      - active
    UserInfo:
      type: object
      properties:
        id:
          type: integer
          format: int32
          description: User ID. Should not be set for new user creation if auto-generated.
            Not allowed to be system-admin-userid (1 or 2) by validation.
        email_id:
          type: string
          format: email
          description: User's email address. Cannot be system admin user email by
            validation.
        roles:
          type: array
          items:
            type: string
          description: List of direct roles assigned to the user (deprecated in favor
            of roleFilters and userRoleGroups).
          nullable: true
        accessToken:
          type: string
          description: Access token for the user (typically not part of create/update
            payload, but in response).
          readOnly: true
          nullable: true
        roleFilters:
          type: array
          items:
            $ref: '#/components/schemas/RoleFilter'
        groups:
          type: array
          items:
            type: string
          description: Deprecated field for user groups. Use userRoleGroups instead.
          nullable: true
        userRoleGroups:
          type: array
          items:
            $ref: '#/components/schemas/UserRoleGroup'
          nullable: true
        superAdmin:
          type: boolean
          description: Indicates if the user has super admin privileges.
        lastLoginTime:
          type: string
          format: date-time
          readOnly: true
          description: The time the user last logged in.
      required:
      - email_id
    RoleGroup:
      type: object
      properties:
        id:
          type: integer
          format: int32
          readOnly: true
        name:
          type: string
          description: Name of the Role group
        description:
          type: string
          description: Descrition fo Role group
          nullable: true
        roleFilters:
          type: array
          description: Role filters
          items:
            $ref: '#/components/schemas/RoleFilter'
        superAdmin:
          type: boolean
          description: Indicates if this role group grants super admin privileges.
      required:
      - name
      - roleFilters
    RoleFilter:
      type: object
      description: Defines a specific permission filter for a role.
      properties:
        entity:
          type: string
          description: The type of entity this filter applies to (e.g., apps, jobs,
            chart-group, cluster).
          enum:
          - apps
          - jobs
          - chart-group
          - cluster
        team:
          type: string
          description: Team associated with this permission. Can be empty for some
            global entities.
          nullable: true
        entityName:
          type: string
          description: Name of the specific entity (e.g., application name, job name,
            chart group name). Can be empty for 'all' access.
          nullable: true
        environment:
          type: string
          description: Environment associated with this permission. Can be empty if
            not applicable.
          nullable: true
        action:
          type: string
          description: Action permitted (e.g., get, create, update, delete, trigger,
            *).
        accessType:
          type: string
          description: Access type, typically for distinguishing app types like devtron-app
            or helm-app.
          enum:
          - devtron-app
          - helm-app
          - ''
          nullable: true
        cluster:
          type: string
          description: Cluster name for cluster-scoped permissions.
          nullable: true
        namespace:
          type: string
          description: Namespace for cluster-scoped permissions.
          nullable: true
        group:
          type: string
          description: API group for K8s resources.
          nullable: true
        kind:
          type: string
          description: Kind of K8s resource.
          nullable: true
        resource:
          type: string
          description: Specific K8s resource name.
          nullable: true
        workflow:
          type: string
          description: Workflow name, applicable if entity is 'jobs'.
          nullable: true
      required:
      - entity
      - action
    UserRoleGroup:
      type: object
      description: Associates a user with a role group.
      properties:
        roleGroup:
          $ref: '#/components/schemas/RoleGroup'
    RbacRoleDto:
      type: object
      description: Represents a default RBAC role.
      properties:
        id:
          type: integer
          format: int32
        roleName:
          type: string
        roleDisplayName:
          type: string
        roleDescription:
          type: string
        entity:
          type: string
          enum:
          - apps
          - cluster
          - chart-group
          - jobs
        accessType:
          type: string
          nullable: true
    BulkDeleteRequest:
      type: object
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int32
          description: List of IDs to delete.
        listingRequest:
          $ref: '#/components/schemas/ListingRequest'
      required:
      - ids
    ListingRequest:
      type: object
      properties:
        searchKey:
          type: string
          nullable: true
        sortOrder:
          type: string
          enum:
          - ASC
          - DESC
          nullable: true
        sortBy:
          type: string
          nullable: true
        offset:
          type: integer
          format: int32
          minimum: 0
          nullable: true
        size:
          type: integer
          format: int32
          minimum: 1
          default: 20
          nullable: true
        showAll:
          type: boolean
          nullable: true
          readOnly: true
    EnvironmentCreateRequest:
      type: object
      required:
      - environment_name
      - cluster_id
      properties:
        environment_name:
          type: string
          maxLength: 50
          description: Name of the Environment
        cluster_id:
          type: integer
          description: Id of the target Cluster
        active:
          type: boolean
          default: true
        default:
          type: boolean
          default: false
        prometheus_endpoint:
          type: string
          description: Prometheus Endpoint of cluster
        namespace:
          type: string
          description: Name of the Namespace which will point to environment
          maxLength: 50
        isClusterCdActive:
          type: boolean
        description:
          type: string
          maxLength: 40
        isVirtualEnvironment:
          type: boolean
          default: false
        allowedDeploymentTypes:
          type: array
          items:
            type: string
            enum:
            - helm
            - argo_cd
    EnvironmentUpdateRequest:
      type: object
      required:
      - id
      - environment_name
      - cluster_id
      properties:
        id:
          type: integer
        environment_name:
          type: string
          description: Name of the Environment
          maxLength: 50
        cluster_id:
          type: integer
          description: Id of the target Cluster
        active:
          type: boolean
        default:
          type: boolean
        prometheus_endpoint:
          type: string
          description: Prometheus Endpoint of cluster
        namespace:
          type: string
          description: Name of the Namespace pointing to environment
          maxLength: 50
        isClusterCdActive:
          type: boolean
        description:
          type: string
          maxLength: 40
        isVirtualEnvironment:
          type: boolean
        allowedDeploymentTypes:
          type: array
          items:
            type: string
            enum:
            - helm
            - argo_cd
    EnvironmentDetail:
      type: object
      properties:
        id:
          type: integer
        environment_name:
          type: string
          description: Name of the Environment
        cluster_id:
          type: integer
          description: Id of the target Cluster
        cluster_name:
          type: string
          description: Name of the cluster
        active:
          type: boolean
        default:
          type: boolean
        prometheus_endpoint:
          type: string
          description: Prometheus Endpoint of cluster
        namespace:
          type: string
          description: Name of the Namespace pointing to environment
        isClusterCdActive:
          type: boolean
        environmentIdentifier:
          type: string
        description:
          type: string
        appCount:
          type: integer
        isVirtualEnvironment:
          type: boolean
        allowedDeploymentTypes:
          type: array
          items:
            type: string
            enum:
            - helm
            - argo_cd
    ClusterWithEnvironments:
      type: object
      properties:
        id:
          type: integer
          description: Cluster ID
        cluster_name:
          type: string
          description: Name of the cluster
        server_url:
          type: string
          description: Server URL of the cluster
        active:
          type: boolean
          description: Whether the cluster is active
        environments:
          type: array
          items:
            $ref: '#/components/schemas/Environment'
        prometheus_url:
          type: string
          description: URL for Prometheus monitoring
        k8sVersion:
          type: string
          description: Kubernetes version of the cluster
    Environment:
      type: object
      properties:
        environmentId:
          type: integer
          description: Environment ID
        environmentName:
          type: string
          description: Name of the environment
        environmentIdentifier:
          type: string
          description: Unique identifier for the environment
        namespace:
          type: string
          description: Namespace associated with the environment
        active:
          type: boolean
          description: Whether the environment is active
    ClusterBean:
      type: object
      properties:
        id:
          type: integer
          description: Id of the cluster
        cluster_name:
          type: string
          description: Name of the cluster
        server_url:
          type: string
          description: Server Url of the cluster
        prometheus_url:
          type: string
          description: Prometheus Endpoint of cluster
        active:
          type: boolean
        config:
          type: object
          properties:
            bearer_token:
              type: string
              description: it will be empty while fetching, and if no change while
                updating
        prometheusAuth:
          $ref: '#/components/schemas/PrometheusAuthGet'
        defaultClusterComponents:
          type: array
          items:
            $ref: '#/components/schemas/DefaultClusterComponentGet'
        k8sversion:
          type: string
          description: K8s version of the cluster
    ClusterAuthDetail:
      type: object
      properties:
        clusterId:
          type: integer
          description: cluster id
        clusterName:
          type: string
          description: cluster name
        errorInConnecting:
          type: string
          description: error message if cluster failed to connect
    Kubeconfig:
      type: object
      description: Kube config of target cluster
      properties:
        config:
          type: string
    ValidateClusterBean:
      type: object
      required:
      - cluster_name
      - server_url
      properties:
        userInfos:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/UserInfos'
        id:
          type: integer
          description: Cluster Id
        cluster_name:
          type: string
          description: Name of the cluster
        server_url:
          type: string
          description: Server Url of the cluster
        prometheus_url:
          type: string
          description: Prometheus Endpoint of the cluster
        active:
          type: boolean
        config:
          type: object
          properties:
            bearer_token:
              type: string
              description: it will be empty while fetching, and if no change while
                updating
            tls_key:
              type: string
              description: it will be empty while fetching, and if no change while
                updating
            cert_data:
              type: string
              description: it will be empty while fetching, and if no change while
                updating
            cert_auth_data:
              type: string
              description: it will be empty while fetching, and if no change while
                updating
        prometheusAuth:
          $ref: '#/components/schemas/PrometheusAuthAdd'
        defaultClusterComponent:
          type: array
          items:
            $ref: '#/components/schemas/DefaultClusterComponentAdd'
        agentInstallationStage:
          type: integer
        k8sVersion:
          type: string
          description: K8s version of the cluster
        userName:
          type: string
        insecure-skip-tls-verify:
          type: boolean
    UserInfos:
      type: object
      properties:
        userName:
          type: string
        config:
          type: object
          additionalProperties:
            type: string
        errorInConnecting:
          type: string
    PrometheusAuthAdd:
      type: object
      properties:
        type:
          type: string
          enum:
          - basic
          - bearer
        basic:
          type: object
          properties:
            username:
              type: string
            password:
              type: string
        bearer:
          type: object
          properties:
            token:
              type: string
    PrometheusAuthGet:
      type: object
      properties:
        userName:
          type: string
        password:
          type: string
        tlsClientCert:
          type: string
        tlsClientKey:
          type: string
    DefaultClusterComponentAdd:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        version:
          type: string
        status:
          type: string
        configuration:
          $ref: '#/components/schemas/ComponentConfiguration'
    DefaultClusterComponentGet:
      type: object
      properties:
        name:
          type: string
        appId:
          type: integer
        installedAppId:
          type: integer
        envId:
          type: integer
        envname:
          type: string
        status:
          type: string
    ComponentConfiguration:
      type: object
      properties:
        type:
          type: string
          enum:
          - yaml
          - json
    CloneApplicationWorkflowRequest:
      type: object
      properties:
        appId:
          type: integer
          description: ID of the application
        appName:
          type: string
          description: Name of the application
        sourceEnvironmentId:
          type: integer
          description: ID of the source environment
        sourceEnvironmentName:
          type: string
          description: Name of the source environment
        targetEnvironmentId:
          type: integer
          description: ID of the target environment
        targetEnvironmentName:
          type: string
          description: Name of the target environment
        cloneEnvInSameWorkflow:
          type: boolean
          description: Flag indicating if the environment should be cloned in the
            same workflow
    CloneApplicationWorkflowResponse:
      type: object
      properties:
        status:
          type: string
          description: Status of the operation
          enum:
          - SUCCESS
          - FAILED
          - SKIPPED
        message:
          type: string
          description: Detailed message about the operation result
      required:
      - status
      - message
    StandardResponse:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code
          example: 200
        status:
          type: string
          description: HTTP status text
          example: OK
        result:
          type: object
          properties:
            status:
              type: string
              description: Status of the operation
              enum:
              - SUCCESS
              - FAILED
              - SKIPPED
            message:
              type: string
              description: Detailed message about the operation result
          required:
          - status
          - message
      required:
      - code
      - status
      - result
    ResourceInfo:
      type: object
      required:
      - podName
      properties:
        podName:
          type: string
          description: Name of the inception pod.
          example: inception-pod-xyz
    TerminalMessage:
      type: object
      properties:
        Op:
          type: string
          description: Operation type for the terminal session.
          example: stdin
        Data:
          type: string
          description: Data for the terminal session (e.g., command or output).
          example: ls -l
        SessionID:
          type: string
          description: ID of the terminal session.
          example: unique-session-id-123
    ResourceRequestObject:
      type: object
      properties:
        appId:
          type: string
          description: Application ID. Used when the request is context-specific to
            an application.
          example: my-app/env-1
        clusterId:
          type: number
          description: Cluster ID. Used when the request is for a direct cluster resource
            (appId is not supplied).
          example: 1
        k8sRequest:
          $ref: '#/components/schemas/K8sRequestObject'
    K8sRequestObject:
      type: object
      properties:
        resourceIdentifier:
          type: object
          properties:
            groupVersionKind:
              $ref: '#/components/schemas/GroupVersionKind'
            namespace:
              type: string
              description: Namespace of the resource.
              example: default
            name:
              type: string
              description: Name of the resource.
              example: my-deployment
          required:
          - name
          - groupVersionKind
        podLogsRequest:
          type: object
          properties:
            containerName:
              type: string
              description: Name of the container for which logs are requested.
              example: my-container
        patch:
          type: string
          description: JSON patch or strategic merge patch to apply to the resource.
          example: '[{"op": "replace", "path": "/spec/replicas", "value": 3}]'
    ResourceGetResponse:
      type: object
      properties:
        manifestResponse:
          $ref: '#/components/schemas/ManifestResponse'
        secretViewAccess:
          type: boolean
          description: 'Indicates whether a user can see obscured secret values or
            not. True if the user has permission, false otherwise.

            '
          example: true
      required:
      - manifestResponse
      - secretViewAccess
    ManifestResponse:
      type: object
      properties:
        manifest:
          type: object
          description: The Kubernetes resource manifest.
          additionalProperties: true
          example:
            apiVersion: v1
            kind: ConfigMap
            metadata:
              name: my-config
              namespace: default
            data:
              key1: value1
              key2: value2
    EventsResponseObject:
      type: object
      description: Represents a Kubernetes Event object.
      properties:
        metadata:
          $ref: '#/components/schemas/ObjectMeta'
        involvedObject:
          $ref: '#/components/schemas/ObjectReference'
        reason:
          type: string
          description: Short, machine-understandable string that describes the reason
            for the transition into the object's current status.
          example: Scheduled
        message:
          type: string
          description: A human-readable description of the status of this operation.
          example: Successfully assigned default/my-pod to node-1
        source:
          $ref: '#/components/schemas/EventSource'
        firstTimestamp:
          type: string
          format: date-time
          description: The time at which the event was first recorded.
        lastTimestamp:
          type: string
          format: date-time
          description: The time at which the most recent occurrence of this event
            was recorded.
        count:
          type: integer
          format: int32
          description: The number of times this event has occurred.
        type:
          type: string
          description: Type of this event (Normal, Warning), new types could be added
            in the future.
          example: Normal
        eventTime:
          type: string
          format: date-time
          nullable: true
          description: MicroTime is version of Time with microsecond level precision.
        reportingComponent:
          type: string
          example: kubelet
        reportingInstance:
          type: string
          example: node-1
    ObjectMeta:
      type: object
      properties:
        name:
          type: string
        namespace:
          type: string
        uid:
          type: string
        resourceVersion:
          type: string
        creationTimestamp:
          type: string
          format: date-time
    ObjectReference:
      type: object
      properties:
        kind:
          type: string
        namespace:
          type: string
        name:
          type: string
        uid:
          type: string
        apiVersion:
          type: string
        resourceVersion:
          type: string
    EventSource:
      type: object
      properties:
        component:
          type: string
        host:
          type: string
    LogsResponseObject:
      type: object
      properties:
        id:
          type: string
          description: Identifier for the log entry (if provided by the stream).
        type:
          type: string
          description: Type of the log entry (e.g., 'stdout', 'stderr').
        data:
          type: string
          description: The actual log line content.
        time:
          type: string
          format: date-time
          description: Timestamp of the log entry.
    GetAllApiResourcesResponse:
      type: object
      properties:
        apiResources:
          type: array
          items:
            $ref: '#/components/schemas/K8sApiResource'
        allowedAll:
          type: boolean
          description: Whether all API resources are allowed for this user.
          example: true
    K8sApiResource:
      type: object
      properties:
        gvk:
          $ref: '#/components/schemas/GroupVersionKind'
        namespaced:
          type: boolean
          description: Whether this API resource is namespace-scoped or cluster-scoped.
          example: true
    GroupVersionKind:
      type: object
      properties:
        group:
          type: string
          description: Group of the API resource.
          example: apps
        version:
          type: string
          description: Version of the API resource.
          example: v1
        kind:
          type: string
          description: Kind of the API resource.
          example: Deployment
      required:
      - group
      - version
      - kind
    ClusterResourceListResponse:
      type: object
      description: Represents a list of resources with dynamic headers and corresponding
        data.
      properties:
        headers:
          type: array
          items:
            type: string
          description: An array of strings representing the column headers for the
            resource list.
          example:
          - NAME
          - NAMESPACE
          - KIND
          - AGE
        data:
          type: array
          items:
            type: object
            additionalProperties:
              type: string
            description: 'An array of objects. Each object represents a resource,
              and its keys correspond to the ''headers''. The values are the resource''s
              data for those headers.

              '
            example:
            - NAME: my-pod-1
              NAMESPACE: default
              KIND: Pod
              AGE: 2d
            - NAME: my-service-abc
              NAMESPACE: kube-system
              KIND: Service
              AGE: 10h
    RotatePodRequest:
      type: object
      properties:
        clusterId:
          type: number
          description: ID of the cluster where resources reside.
          example: 1
        resources:
          type: array
          items:
            type: object
            properties:
              groupVersionKind:
                $ref: '#/components/schemas/GroupVersionKind'
              namespace:
                type: string
                description: Namespace of the resource.
                example: production
              name:
                type: string
                description: Name of the resource (e.g., Deployment, StatefulSet name).
                example: my-app-deployment
            required:
            - name
            - groupVersionKind
            - namespace
      required:
      - clusterId
      - resources
    RotatePodResponse:
      type: object
      properties:
        containsError:
          type: boolean
          description: True if any error occurred during the rotation of one or more
            pods.
          example: false
        responses:
          type: array
          items:
            type: object
            properties:
              groupVersionKind:
                $ref: '#/components/schemas/GroupVersionKind'
              namespace:
                type: string
                description: Namespace of the resource.
              name:
                type: string
                description: Name of the resource.
              errorResponse:
                type: string
                nullable: true
                description: Error message if rotation failed for this specific resource.
                  Otherwise null.
                example: failed to find resource
    ApplyResourcesRequest:
      type: object
      properties:
        clusterId:
          type: number
          description: ID of the cluster where resources will be applied.
          example: 1
        manifest:
          type: string
          description: 'A string containing one or more Kubernetes resource manifests,
            separated by ''---''.

            '
          example: "apiVersion: v1\nkind: ConfigMap\nmetadata:\n  name: my-cm\ndata:\n\
            \  key: value\n---\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n\
            \  name: my-app\nspec:\n  replicas: 1\n  selector:\n    matchLabels:\n\
            \      app: my-app\n  template:\n    metadata:\n      labels:\n      \
            \  app: my-app\n    spec:\n      containers:\n      - name: nginx\n  \
            \      image: nginx\n"
      required:
      - clusterId
      - manifest
    ApplyResourcesResponse:
      type: object
      properties:
        kind:
          type: string
          description: Kind of the resource that was applied.
          example: Deployment
        name:
          type: string
          description: Name of the resource that was applied.
          example: my-app
        error:
          type: string
          nullable: true
          description: Error message if applying this specific resource failed. Otherwise
            null.
          example: null
        isUpdate:
          type: boolean
          description: True if the resource was updated, false if it was created (or
            no change).
          example: true
      required:
      - kind
      - name
      - isUpdate
    AppWorkflowDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the workflow.
          readOnly: true
        name:
          type: string
          description: Name of the workflow.
        appId:
          type: integer
          description: ID of the application this workflow belongs to.
        tree:
          type: array
          items:
            $ref: '#/components/schemas/AppWorkflowMappingDto'
    AppWorkflowMappingDto:
      type: object
      properties:
        id:
          type: integer
          description: ID of the workflow mapping.
          readOnly: true
        appWorkflowId:
          type: integer
          description: ID of the parent application workflow.
        type:
          type: string
          description: Type of the component (e.g., CI_PIPELINE, CD_PIPELINE).
        componentId:
          type: integer
          description: ID of the component (e.g., CiPipelineId, CdPipelineId).
        parentId:
          type: integer
          description: ID of the parent component in the workflow tree.
        parentType:
          type: string
          description: Type of the parent component in the workflow tree.
        deploymentAppDeleteRequest:
          type: boolean
          description: Indicates if a deployment app delete request is associated.
        isLast:
          type: boolean
          description: Indicates if this is the last node in its branch of the tree.
    GitOpsConfigDto:
      type: object
      properties:
        id:
          type: integer
          description: GitOps Id (null for new configuration)
        provider:
          type: string
          description: Gitops provider
          example: github,gitlabs
        username:
          type: string
          description: Username of GitOps provider
        token:
          type: string
          description: Authentication token of GitOps provider
        gitLabGroupId:
          type: string
          description: Group Id of gitLab
        gitHubOrgId:
          type: string
          description: Group Id of gitHub
        host:
          type: string
          description: Host of GitOps provider
        active:
          type: boolean
        azureProjectName:
          type: string
          description: Project Name of Azure
        userId:
          type: integer
          description: User Id of GitOps provider
    DetailedError:
      type: object
      properties:
        successfulStages:
          type: array
          items:
            type: string
          description: All successful stages
        validatedOn:
          type: string
          description: Timestamp of validation
        stageErrorMap:
          type: array
          items:
            type: object
            properties:
              stage:
                type: string
              error:
                type: string
          description: map of stage and their respective errors
    NotificationSetting:
      type: object
      required:
      - configName
      properties:
        id:
          type: integer
          description: Unique id
        configName:
          type: string
          description: Unique name of group
        appId:
          type: integer
          description: app id
        envId:
          type: integer
          description: env id
        pipelineIds:
          type: array
          items:
            type: integer
        eventTypeIds:
          type: array
          items:
            type: integer
        pipelineType:
          type: string
          description: pipeline type CI or CD
        providers:
          type: array
          items:
            $ref: '#/components/schemas/providers'
          description: role filters objects
    providers:
      type: object
      required:
      - dest
      properties:
        dest:
          type: string
          description: channel destination name
        rule:
          type: string
          description: rule
        configId:
          type: integer
          description: config id
    NotificationConfig:
      type: object
      required:
      - channel
      properties:
        channel:
          type: string
          description: channel type
          enum:
          - slack
          - ses
        configs:
          type: array
          items:
            $ref: '#/components/schemas/configs'
          description: config holds for either slack or ses
    NotificationConfigResponse:
      type: object
      properties:
        slackConfigs:
          type: array
          items:
            $ref: '#/components/schemas/configs'
          description: config holds for either slack or ses
        sesConfigs:
          type: array
          items:
            $ref: '#/components/schemas/configs'
          description: config holds for either slack or ses
    configs:
      type: object
      required:
      - type
      - configName
      properties:
        id:
          type: integer
          description: unique id for config either slack or ses on response or update
            only
        type:
          type: string
          description: channel destination type, slack or ses
        configName:
          type: string
          description: configName
        secretKey:
          type: string
          description: secretKey, only in case of ses
        accessKey:
          type: string
          description: accessKey, only in case of ses
        fromEmail:
          type: string
          description: fromEmail, only in case of ses
        region:
          type: string
          description: region, only in case of ses
        webhookUrl:
          type: string
          description: webhook url, only fill in case of type is slack
        teamId:
          type: integer
          description: project id, only fill in case of type is slack
        userId:
          type: integer
          description: project id, only fill in case of type is slack
    entity:
      type: object
      properties:
        id:
          type: integer
          description: it contains entity id
        name:
          type: string
          description: it contains entity name
    HistoryComponentDetailDto:
      type: object
      properties:
        values:
          type: array
          items:
            $ref: '#/components/schemas/HistoryComponentValuesDto'
        codeEditorValue:
          type: object
          properties:
            displayName:
              type: string
            value:
              type: string
    HistoryComponentValuesDto:
      type: object
      properties:
        fieldName:
          type: object
          properties:
            displayName:
              type: string
            value:
              type: string
    HistoryComponentListDto:
      type: object
      properties:
        id:
          type: integer
        deployedOn:
          type: string
          format: timestamp
        deployedBy:
          type: string
        deploymentStatus:
          type: string
    HistoryConfigurationListDto:
      type: array
      items:
        $ref: '#/components/schemas/HistoryConfiguration'
    HistoryConfiguration:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
          enum:
          - DEPLOYMENT_TEMPLATE
          - CONFIGMAP
          - SECRET
          - PIPELINE_STRATEGY
        childList:
          type: array
          items:
            type: string
  requestBodies:
    SSOLoginDto:
      description: SSO Login Configuration object
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SSOLoginDto'
    UserInfo:
      description: User Information object
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UserInfo'
    RoleGroup:
      description: Role Group object
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RoleGroup'
    BulkDeleteRequest:
      description: Request for bulk deletion of items.
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BulkDeleteRequest'
  responses:
    SSOLoginConfigResponse:
      description: Response for SSO Login Configuration.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/SSOLoginDto'
    UserInfoResponse:
      description: Response containing User Information.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/UserInfo'
    UserListingResponse:
      description: Paginated list of users.
      content:
        application/json:
          schema:
            type: object
            properties:
              users:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfo'
              totalCount:
                type: integer
                format: int32
    RoleGroupResponse:
      description: Response containing Role Group information.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RoleGroup'
    RoleGroupListingResponse:
      description: Paginated list of role groups.
      content:
        application/json:
          schema:
            type: object
            properties:
              roleGroups:
                type: array
                items:
                  $ref: '#/components/schemas/RoleGroup'
              totalCount:
                type: integer
                format: int32
    GenericSuccess:
      description: Generic success response.
      content:
        application/json:
          schema:
            type: object
            properties:
              success:
                type: boolean
                example: true
    BadRequest:
      description: Bad request. Invalid input parameters.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized. User is not logged in or token is invalid.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden. User does not have permission.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotAcceptable:
      description: Not Acceptable. Request cannot be processed.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalServerError:
      description: Internal server error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  parameters:
    PathId:
      name: id
      in: path
      required: true
      description: Identifier (typically integer ID).
      schema:
        type: integer
        format: int32
    SearchKeyQuery:
      name: searchKey
      in: query
      required: false
      description: Search term.
      schema:
        type: string
    SortOrderQuery:
      name: sortOrder
      in: query
      required: false
      description: Sort order (ASC or DESC).
      schema:
        type: string
        enum:
        - ASC
        - DESC
    SortByQueryUser:
      name: sortBy
      in: query
      required: false
      description: Field to sort users by (e.g., email_id, last_login).
      schema:
        type: string
        enum:
        - email_id
        - last_login
    SortByQueryRoleGroup:
      name: sortBy
      in: query
      required: false
      description: Field to sort role groups by (e.g., name).
      schema:
        type: string
        enum:
        - name
    OffsetQuery:
      name: offset
      in: query
      required: false
      description: Offset for pagination.
      schema:
        type: integer
        format: int32
        minimum: 0
    SizeQuery:
      name: size
      in: query
      required: false
      description: Number of items per page.
      schema:
        type: integer
        format: int32
        minimum: 1
        default: 20
x-tagGroups:
- name: Common Devtron automation APIs
  tags:
  - Metadata
  - Jobs
  - Helm Charts
  - List Applications
  - Applications
  - Labels
  - bulk_other
  - BulkUpdate
  - SSO Configuration
  - User Management
  - Role Group Management
  - RBAC
  - Authentication
  - Policy Management
  - Cache Management
  - Cluster Environment
  - Cluster Management
  - Environment Management
  - Change Chart
  - Clone Workflow
  - Deployment History
  - K8s Resource
  - Workflow Management
  - Devtron Server version
  - GitOps Validation
  - Notifications
