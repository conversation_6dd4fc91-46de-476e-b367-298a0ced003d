openapi: "3.0.0"
info:
  version: 1.0.0
  title: Devtron Labs
  description: Devtron API for user management
  termsOfService: https://devtron.ai/terms/
  contact:
    name: Devtron Labs
    email: <EMAIL>
    url: https://devtron.ai
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html
servers:
  - url: http://localhost:8080
paths:
  /orchestrator/user/v2:
    get:
      description: Get all users with filters
      parameters:
        - name: searchKey
          in: query
          description: Search key for user listing
          required: false
          schema:
            type: string
        - name: sortOrder
          in: query
          description: Sorting order (ASC or DESC)
          required: false
          schema:
            type: string
            enum: [ASC, DESC]
        - name: sortBy
          in: query
          description: Sorting by email_id or last_login
          required: false
          schema:
            type: string
            enum: [email_id, last_login]
        - name: offset
          in: query
          description: Offset for paginating the results
          required: false
          schema:
            type: integer
        - name: size
          in: query
          description: Size of the result set
          required: false
          schema:
            type: integer
        - name: showAll
          in: query
          description: Show all users (boolean)
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListingResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /orchestrator/user:
    post:
      description: Create a new user
      requestBody:
        description: User details
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      description: Update an existing user
      requestBody:
        description: Updated user details
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /orchestrator/user/{id}:
    get:
      description: Get user details by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            description: User ID
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      description: Delete a user
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
            description: User ID
      responses:
        '200':
          description: User deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /orchestrator/user/bulk:
    delete:
      description: Delete multiple users in bulk
      requestBody:
        description: List of user IDs to delete
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkDeleteRequest'
      responses:
        '200':
          description: Users deleted successfully
          content:
            application/json:
              schema:
                type: boolean
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    User:
      type: object
      required:
        - email_id
      properties:
        id:
          type: integer
          description: Unique ID of user
        email_id:
          type: string
          description: Unique valid email ID of user
        userRoleGroups:
          type: array
          items:
            $ref: '#/components/schemas/UserRoleGroup'
        roleFilters:
          type: array
          items:
            $ref: '#/components/schemas/RoleFilter'
          description: Role filters objects
        createdOn:
          type: string
          format: date-time
          description: Creation timestamp
        createdBy:
          type: string
          description: Creator's name
        updatedOn:
          type: string
          format: date-time
          description: Last update timestamp
        updatedBy:
          type: string
          description: Last updater's name

    UserRoleGroup:
      type: object
      properties:
        id:
          type: integer
          description: Role group ID
        name:
          type: string
          description: Role group name
        description:
          type: string
          description: Role group description
        createdOn:
          type: string
          format: date-time
          description: Creation timestamp
        createdBy:
          type: string
          description: Creator's name
        updatedOn:
          type: string
          format: date-time
          description: Last update timestamp
        updatedBy:
          type: string
          description: Last updater's name

    RoleFilter:
      type: object
      properties:
        entity:
          type: string
          description: Entity type (e.g. app, env, cluster)
        entityName:
          type: string
          description: Entity name
        action:
          type: string
          description: Action type (e.g. create, update, delete)
        accessType:
          type: string
          description: Access type (e.g. admin, manager, viewer)
        team:
          type: string
          description: Team name
        environment:
          type: string
          description: Environment name
        cluster:
          type: string
          description: Cluster name

    UserListingResponse:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
          description: List of users
        totalCount:
          type: integer
          description: Total number of users

    BulkDeleteRequest:
      type: object
      required:
        - ids
      properties:
        ids:
          type: array
          items:
            type: integer
          description: List of user IDs to delete
        loggedInUserId:
          type: integer
          description: ID of the user performing the deletion

    ErrorResponse:
      required:
        - code
        - message
      properties:
        code:
          type: integer
          description: Error code
        message:
          type: string
          description: Error message 