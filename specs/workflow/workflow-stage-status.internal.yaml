openapi: 3.0.0
info:
  title: Workflow Status API for showing execution stage
  version: 1.0.0
paths:
  /orchestrator/workflow/status: #this is not real API, only for sharing purpose
    get:
      summary: Get Workflow Status
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWorkflowStatusResponse'
              example: '{"status":"In progress","startTime":"1","endTime":"","message":"e-message","podStatus":"Running","podName":"pod-name","workflowExecutionStages":{"workflow":[{"stageName":"Preparation","status":"SUCCESS","startTime":"1","endTime":"2","message":"p-message","metadata":{}},{"stageName":"Execution","status":"STARTED","startTime":"2","endTime":"","message":"e-message","metadata":{}}],"pod":[{"stageName":"Execution","status":"STARTED","startTime":"2","endTime":"","message":"e-message","metadata":{"clusterId":"123","podName":"pod-name"}}]}}'

components:
  schemas:
    GetWorkflowStatusResponse:
      type: object
      properties:
        status:
          type: string
          description: Workflow current status - for backward compatibility
        startTime:
          type: string
          format: date-time
          description: Workflow start time
        endTime:
          type: string
          format: date-time
          description: Workflow end time
        message:
          type: string
          description: Workflow message
        podStatus:
          type: string
          description: Pod status
        podName:
          type: string
          description: Pod name
        workflowExecutionStages:
          $ref: '#/components/schemas/WorkflowStages'

    WorkflowStages:
      type: object
      properties:
        workflow:
          type: array
          items:
            type: object
            properties:
              stageName:
                type: string
                description: Preparation/Execution
              status:
                type: string
                enum: [NOT_STARTED, RUNNING, SUCCEEDED, FAILED, ABORTED, TIMEOUT, UNKNOWN]
              startTime:
                type: string
                format: date-time
              endTime:
                type: string
                format: date-time
              message:
                type: string
              metadata:
                type: object
        pod:
          type: array
          items:
            type: object
            properties:
              stageName:
                type: string
                description: Execution
              status:
                type: string
                enum: [NOT_STARTED, RUNNING, SUCCEEDED, FAILED, ABORTED, TIMEOUT, UNKNOWN]
              startTime:
                type: string
                format: date-time
              endTime:
                type: string
                format: date-time
              message:
                type: string
              metadata:
                type: object
                properties:
                  clusterId:
                    type: string
                    description: Cluster ID
                  podName:
                    type: string
                    description: Pod name