#!/bin/bash

# Script to fix API endpoints by adding /orchestrator prefix
# This script processes one endpoint at a time and logs the results

LOG_FILE="api_fix_log.txt"
SWAGGER_FILE="specs/swagger/openapi.yaml"

# Initialize log file
echo "API Endpoint Fix Log - $(date)" > $LOG_FILE
echo "=================================" >> $LOG_FILE

# Function to fix an endpoint
fix_endpoint() {
    local old_path="$1"
    local new_path="/orchestrator$1"
    
    echo "Processing: $old_path -> $new_path"
    
    # Check if the endpoint exists in the file
    if grep -q "^  $old_path:" "$SWAGGER_FILE"; then
        # Replace the endpoint
        sed -i.bak "s|^  $old_path:|  $new_path:|" "$SWAGGER_FILE"
        
        if [ $? -eq 0 ]; then
            echo "✅ SUCCESS: Fixed $old_path -> $new_path" | tee -a $LOG_FILE
            return 0
        else
            echo "❌ FAILED: Could not fix $old_path" | tee -a $LOG_FILE
            return 1
        fi
    else
        echo "⚠️  WARNING: Endpoint $old_path not found in file" | tee -a $LOG_FILE
        return 1
    fi
}

# List of endpoints that need fixing (missing /orchestrator prefix)
endpoints_to_fix=(
    "/resource/history/deployment/cd-pipeline/v1"
    "/app/labels/list"
    "/batch/{apiVersion}/{kind}/readme"
    "/batch/v1beta1/application/dryrun"
    "/batch/v1beta1/hibernate"
    "/batch/v1beta1/unhibernate"
    "/batch/v1beta1/deploy"
    "/batch/v1beta1/build"
    "/batch/v1beta1/application"
    "/sso/create"
    "/sso/update"
    "/sso/list"
    "/sso/{id}"
    "/sso"
    "/rbac/roles/default"
    "/api/v1/session"
    "/refresh"
    "/admin/policy/default"
    "/devtron/auth/verify"
    "/devtron/auth/verify/v2"
    "/user/v2"
    "/user"
    "/user/{id}"
    "/user/v2/{id}"
    "/user/bulk"
    "/user/detail/get"
    "/user/role/group"
    "/user/role/group/{id}"
    "/user/role/group/detailed/get"
    "/user/role/group/search"
    "/user/role/group/bulk"
    "/user/check/roles"
    "/user/sync/orchestratortocasbin"
    "/user/update/trigger/terminal"
    "/user/role/cache"
    "/user/role/cache/invalidate"
    "/login"
    "/auth/login"
    "/auth/callback"
    "/api/dex/{path}"
    "/env"
    "/cluster/delete"
    "/env/delete"
    "/env/clusters"
    "/cluster/{cluster_id}/env"
    "/cluster"
    "/cluster/auth-list"
    "/cluster/validate"
    "/cluster/saveClusters"
    "/app/env/patch"
    "/app/workflow/clone"
    "/k8s/resource"
    "/k8s/resource/create"
    "/k8s/resource/delete"
    "/k8s/events"
    "/k8s/pods/logs/{podName}"
    "/k8s/pod/exec/session/{identifier}/{namespace}/{pod}/{shell}/{container}"
    "/k8s/api-resources/{clusterId}"
    "/k8s/resource/list"
    "/k8s/resources/rotate"
    "/k8s/resources/apply"
    "/app/workflow"
    "/app/workflow/{app-wf-id}/app/{app-id}"
    "/version"
    "/validate"
    "/config"
    "/notification"
    "/notification/recipient"
    "/notification/channel"
)

echo "Found ${#endpoints_to_fix[@]} endpoints to fix"
echo "Starting endpoint fixes..." | tee -a $LOG_FILE

success_count=0
failed_count=0

for endpoint in "${endpoints_to_fix[@]}"; do
    echo ""
    echo "--- Processing endpoint: $endpoint ---"
    
    if fix_endpoint "$endpoint"; then
        ((success_count++))
    else
        ((failed_count++))
    fi
    
    echo "Press Enter to continue to next endpoint, or 'q' to quit..."
    read -r input
    if [ "$input" = "q" ]; then
        echo "User requested to stop. Exiting..."
        break
    fi
done

echo ""
echo "=== SUMMARY ===" | tee -a $LOG_FILE
echo "Total endpoints processed: ${#endpoints_to_fix[@]}" | tee -a $LOG_FILE
echo "Successful fixes: $success_count" | tee -a $LOG_FILE
echo "Failed fixes: $failed_count" | tee -a $LOG_FILE
echo "Log saved to: $LOG_FILE"

# Clean up backup file
if [ -f "${SWAGGER_FILE}.bak" ]; then
    rm "${SWAGGER_FILE}.bak"
fi 